"""
消融实验启动脚本
用于快速启动消融实验，对比不同模型架构的性能
"""
import os
import sys
import subprocess
import datetime

def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("🧪 卫星星座任务规划消融实验")
    print("=" * 80)
    print("本实验将对比以下三种模型架构:")
    print("  1. GPN+IndRNN+Transformer  - 当前基线模型")
    print("  2. GPN+LSTM+Transformer    - 对比RNN类型的影响")
    print("  3. PN+IndRNN               - 对比网络架构的影响")
    print()
    print("每种模型将在三种星座模式下训练:")
    print("  • Cooperative (协同模式)")
    print("  • Competitive (竞争模式)")
    print("  • Hybrid (混合模式)")
    print()
    print("总计: 3种模型 × 3种模式 = 9个训练任务")
    print("=" * 80)

def get_user_confirmation():
    """获取用户确认"""
    print("⚠️  注意事项:")
    print("  • 完整的消融实验可能需要6-12小时")
    print("  • 建议在GPU环境下运行")
    print("  • 确保有足够的磁盘空间存储结果")
    print("  • 实验过程中请勿关闭终端")
    print()
    
    while True:
        response = input("是否继续运行消融实验? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            return True
        elif response in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y 或 n")

def run_quick_test():
    """运行快速测试"""
    print("🔍 运行快速测试验证实验环境...")
    
    try:
        # 运行测试脚本
        result = subprocess.run([
            sys.executable, 'test_ablation_study.py'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 快速测试通过！")
            return True
        else:
            print("❌ 快速测试失败:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 快速测试超时")
        return False
    except Exception as e:
        print(f"❌ 快速测试出错: {e}")
        return False

def run_ablation_experiment():
    """运行消融实验"""
    print("🚀 开始消融实验...")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 构建命令
    cmd = [
        sys.executable, 
        'train_multi_constellation_modes.py',
        '--ablation_study',
        '--epochs', '50',  # 可以根据需要调整
        '--batch_size', '128',
        '--num_nodes', '50',
        '--num_satellites', '3'
    ]
    
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 运行消融实验
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时输出结果
        for line in process.stdout:
            print(line, end='')
        
        process.wait()
        
        if process.returncode == 0:
            print("\n🎉 消融实验成功完成！")
            return True
        else:
            print(f"\n❌ 消融实验失败，退出码: {process.returncode}")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了实验")
        process.terminate()
        return False
    except Exception as e:
        print(f"\n❌ 实验过程中出错: {e}")
        return False

def show_results_info():
    """显示结果信息"""
    print("\n📊 实验结果:")
    print("  实验结果将保存在 ablation_study_TIMESTAMP/ 目录中")
    print("  包含以下内容:")
    print("    • 9个模型训练结果目录")
    print("    • ablation_comparison_results/ - 对比分析结果")
    print("      ├── ablation_results.json - 详细数据")
    print("      ├── ablation_report.txt - 文本报告")
    print("      └── ablation_performance_comparison.png - 性能对比图")
    print()
    print("📈 分析建议:")
    print("  1. 查看 ablation_report.txt 了解各模型性能")
    print("  2. 查看对比图了解模型差异")
    print("  3. 根据结果选择最优模型架构")

def main():
    """主函数"""
    print_banner()
    
    # 检查是否在正确的目录
    if not os.path.exists('train_multi_constellation_modes.py'):
        print("❌ 错误: 请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 获取用户确认
    if not get_user_confirmation():
        print("👋 实验已取消")
        sys.exit(0)
    
    # 运行快速测试
    if not run_quick_test():
        print("❌ 快速测试失败，请检查环境配置")
        sys.exit(1)
    
    print()
    print("=" * 80)
    
    # 运行消融实验
    success = run_ablation_experiment()
    
    if success:
        show_results_info()
        print("\n🎊 消融实验全部完成！")
    else:
        print("\n💔 消融实验未能完成，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
