#!/usr/bin/env python3
"""
测试简化架构：GPN+LSTM（无Transformer）
"""
import os
import sys
import torch
import datetime
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from train_multi_constellation_modes import (
    EnhancedTrainingLogger, 
    train_constellation_with_enhanced_learning,
    create_model_for_mode
)
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_simplified_architecture():
    """测试简化的GPN+LSTM架构"""
    print("🧪 测试简化架构：GPN+LSTM（无Transformer）")
    print("=" * 60)
    
    # 创建测试目录
    test_dir = f"test_simplified_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(test_dir, exist_ok=True)
    
    print(f"📁 测试结果将保存到: {test_dir}")
    
    # 创建小规模数据集用于快速测试
    print("\n📊 创建测试数据集...")
    train_data = ConstellationSMPDataset(
        size=20,  # 小规模测试
        num_samples=100,
        seed=1234,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=args.num_satellites
    )
    
    valid_data = ConstellationSMPDataset(
        size=20,
        num_samples=50,
        seed=1235,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=args.num_satellites
    )
    
    print(f"✓ 训练数据: {len(train_data)} 样本")
    print(f"✓ 验证数据: {len(valid_data)} 样本")
    
    # 测试不同架构配置
    test_configs = [
        {
            'name': 'GPN+LSTM',
            'use_transformer': False,
            'rnn': 'lstm',
            'description': '简化架构：GPN+LSTM（无Transformer）'
        },
        {
            'name': 'GPN+IndRNN',
            'use_transformer': False,
            'rnn': 'indrnn',
            'description': '对比基准：GPN+IndRNN（无Transformer）'
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🚀 测试配置: {config['name']}")
        print(f"   描述: {config['description']}")
        print("-" * 50)
        
        # 创建配置专用目录
        config_dir = os.path.join(test_dir, config['name'].lower().replace('+', '_'))
        os.makedirs(config_dir, exist_ok=True)
        
        try:
            # 创建模型
            print("🔧 创建模型...")
            actor, critic = create_model_for_mode(
                constellation_mode='cooperative',
                train_data=train_data,
                model_type='gpn',
                rnn_type=config['rnn'],
                use_transformer=config['use_transformer'],
                transformer_config=None
            )
            
            # 计算参数数量
            actor_params = sum(p.numel() for p in actor.parameters())
            critic_params = sum(p.numel() for p in critic.parameters())
            total_params = actor_params + critic_params
            
            print(f"✓ Actor参数: {actor_params:,}")
            print(f"✓ Critic参数: {critic_params:,}")
            print(f"✓ 总参数: {total_params:,}")
            
            # 创建增强版日志记录器
            logger = EnhancedTrainingLogger('cooperative', config['name'], config_dir)
            
            # 训练参数
            training_params = {
                'task': args.task,
                'num_nodes': 20,
                'train_data': train_data,
                'valid_data': valid_data,
                'reward_fn': reward,
                'render_fn': render,
                'batch_size': 16,  # 小批次用于快速测试
                'actor_lr': 1e-4,  # 稍高的学习率用于快速测试
                'critic_lr': 1e-4,
                'max_grad_norm': 1.0,  # 更严格的梯度裁剪
                'attention': args.attention,
                'epochs': 1,  # 只训练1个epoch用于测试
                'num_satellites': args.num_satellites,
                'save_dir': config_dir,
                'weight_decay': args.weight_decay,
                'verbose': True,
                'constellation_mode': 'cooperative'
            }
            
            print(f"🏃 开始训练（1个epoch，快速测试）...")
            print(f"   学习率: {training_params['actor_lr']}")
            print(f"   批次大小: {training_params['batch_size']}")
            print(f"   梯度裁剪: {training_params['max_grad_norm']}")
            
            # 开始训练
            start_time = datetime.datetime.now()
            best_reward, training_stats = train_constellation_with_enhanced_learning(
                actor, critic, logger, **training_params
            )
            end_time = datetime.datetime.now()
            
            training_time = (end_time - start_time).total_seconds()
            
            # 计算统计信息
            avg_reward = np.mean(training_stats['rewards']) if training_stats['rewards'] else 0
            avg_revenue_rate = np.mean(training_stats['revenue_rates']) if training_stats['revenue_rates'] else 0
            avg_loss = np.mean(training_stats['losses']) if training_stats['losses'] else 0
            
            # 保存结果
            results[config['name']] = {
                'best_reward': best_reward,
                'avg_reward': avg_reward,
                'avg_revenue_rate': avg_revenue_rate,
                'avg_loss': avg_loss,
                'total_params': total_params,
                'training_time': training_time,
                'batches_trained': len(training_stats['rewards']),
                'config': config
            }
            
            print(f"✅ 训练完成!")
            print(f"   最佳奖励: {best_reward:.4f}")
            print(f"   平均奖励: {avg_reward:.4f}")
            print(f"   平均收益率: {avg_revenue_rate:.4f}")
            print(f"   平均损失: {avg_loss:.4f}")
            print(f"   训练时间: {training_time:.1f}秒")
            print(f"   训练批次: {len(training_stats['rewards'])}")
            
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            import traceback
            traceback.print_exc()
            results[config['name']] = {'error': str(e)}
    
    # 对比结果
    print(f"\n📊 架构对比结果")
    print("=" * 60)
    
    if len(results) >= 2:
        configs = list(results.keys())
        
        print(f"{'指标':<20} {'GPN+LSTM':<15} {'GPN+IndRNN':<15} {'差异':<15}")
        print("-" * 65)
        
        for metric in ['best_reward', 'avg_reward', 'avg_revenue_rate', 'avg_loss', 'total_params', 'training_time']:
            if metric in results[configs[0]] and metric in results[configs[1]]:
                val1 = results[configs[0]][metric]
                val2 = results[configs[1]][metric]
                
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    if val2 != 0:
                        diff_pct = ((val1 - val2) / val2) * 100
                        print(f"{metric:<20} {val1:<15.4f} {val2:<15.4f} {diff_pct:+.1f}%")
                    else:
                        print(f"{metric:<20} {val1:<15.4f} {val2:<15.4f} {'N/A':<15}")
    
    # 结论
    print(f"\n🎯 测试结论")
    print("=" * 60)
    
    successful_configs = [name for name, result in results.items() if 'error' not in result]
    
    if successful_configs:
        print(f"✅ 成功测试的架构: {', '.join(successful_configs)}")
        
        # 找出最佳配置
        best_config = max(successful_configs, key=lambda x: results[x]['best_reward'])
        best_result = results[best_config]
        
        print(f"🏆 最佳架构: {best_config}")
        print(f"   最佳奖励: {best_result['best_reward']:.4f}")
        print(f"   参数数量: {best_result['total_params']:,}")
        print(f"   训练时间: {best_result['training_time']:.1f}秒")
        
        print(f"\n📋 建议:")
        if 'GPN+LSTM' in successful_configs:
            lstm_result = results['GPN+LSTM']
            print(f"• GPN+LSTM架构可行，参数数量: {lstm_result['total_params']:,}")
            print(f"• 建议进行完整的3个epoch训练以获得更可靠的结果")
            
        if len(successful_configs) >= 2:
            print(f"• 可以进行完整的消融实验对比")
            print(f"• 建议监控长期学习趋势")
    else:
        print("❌ 所有配置都失败了，需要检查模型实现")
    
    print(f"\n📁 详细结果保存在: {test_dir}")
    return results

if __name__ == '__main__':
    results = test_simplified_architecture()
    print("\n🎉 架构简化测试完成!")
