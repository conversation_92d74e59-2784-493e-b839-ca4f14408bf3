"""
测试重构后的PN+IndRNN模型
验证方案B的修复效果
"""
import os
import sys
import torch
import numpy as np
import time
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.pn_constellation import PNConstellation, ConstellationStateCriticPN
from constellation_smp.gpn_constellation import GPNConstellation

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_basic_functionality():
    """测试基本功能"""
    print("🔧 测试基本功能")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(20, 5, 12345, 0.3, 5, 3)
    test_loader = DataLoader(dataset, batch_size=2, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    static = static.to(device)
    dynamic = dynamic.to(device)
    
    print(f"输入数据维度:")
    print(f"  static: {static.shape}")
    print(f"  dynamic: {dynamic.shape}")
    
    try:
        # 创建重构的PN模型
        actor = PNConstellation(
            static_size=static.size(1),
            dynamic_size=dynamic.size(1),
            hidden_size=128,
            num_satellites=3,
            rnn='indrnn',
            num_layers=2,
            constellation_mode='cooperative',
            update_fn=dataset.update_dynamic,
            mask_fn=dataset.update_mask
        ).to(device)
        
        # 创建Critic
        critic = ConstellationStateCriticPN(
            static_size=static.size(1),
            dynamic_size=dynamic.size(1),
            hidden_size=128,
            num_satellites=3,
            constellation_mode='cooperative'
        ).to(device)
        
        print(f"✅ 模型创建成功")
        print(f"  Actor参数数量: {sum(p.numel() for p in actor.parameters()):,}")
        print(f"  Critic参数数量: {sum(p.numel() for p in critic.parameters()):,}")
        
        # 测试前向传播
        with torch.no_grad():
            tour_indices, satellite_indices, tour_logp, satellite_logp = actor(static, dynamic)
            critic_value = critic(static, dynamic)
        
        print(f"✅ 前向传播成功")
        print(f"  tour_indices: {tour_indices.shape}")
        print(f"  satellite_indices: {satellite_indices.shape}")
        print(f"  critic_value: {critic_value.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_constraint_compliance():
    """测试约束遵守情况"""
    print("\n🔍 测试约束遵守情况")
    print("=" * 50)
    
    # 创建严格资源限制的测试数据
    dataset = ConstellationSMPDataset(15, 10, 12345, 0.1, 2, 3)  # 严格限制
    test_loader = DataLoader(dataset, batch_size=3, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    static = static.to(device)
    dynamic = dynamic.to(device)
    
    try:
        actor = PNConstellation(
            static_size=static.size(1),
            dynamic_size=dynamic.size(1),
            hidden_size=128,
            num_satellites=3,
            rnn='indrnn',
            num_layers=2,
            constellation_mode='cooperative',
            update_fn=dataset.update_dynamic,
            mask_fn=dataset.update_mask
        ).to(device)
        
        revenue_rates = []
        for i in range(10):
            with torch.no_grad():
                tour_indices, satellite_indices, _, _ = actor(static, dynamic)
                _, revenue_rate, _, _, _ = reward(static, tour_indices, satellite_indices, 'cooperative')
                revenue_rates.append(revenue_rate.mean().item())
        
        avg_revenue = np.mean(revenue_rates)
        max_revenue = np.max(revenue_rates)
        
        print(f"收益率统计:")
        print(f"  平均收益率: {avg_revenue:.4f}")
        print(f"  最大收益率: {max_revenue:.4f}")
        print(f"  标准差: {np.std(revenue_rates):.4f}")
        
        if max_revenue <= 1.0:
            print("✅ 约束遵守测试通过")
            return True
        else:
            print("❌ 约束违反：收益率超过1.0")
            return False
            
    except Exception as e:
        print(f"❌ 约束测试失败: {e}")
        return False

def test_computation_efficiency():
    """测试计算效率"""
    print("\n⚡ 测试计算效率")
    print("=" * 50)
    
    # 创建中等规模的测试数据
    dataset = ConstellationSMPDataset(50, 5, 12345, 0.3, 5, 3)
    test_loader = DataLoader(dataset, batch_size=4, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    static = static.to(device)
    dynamic = dynamic.to(device)
    
    try:
        # 重构的PN模型
        pn_actor = PNConstellation(
            static_size=static.size(1),
            dynamic_size=dynamic.size(1),
            hidden_size=128,
            num_satellites=3,
            rnn='indrnn',
            num_layers=2,
            constellation_mode='cooperative',
            update_fn=dataset.update_dynamic,
            mask_fn=dataset.update_mask
        ).to(device)
        
        # GPN模型作为对比
        gpn_actor = GPNConstellation(
            static_size=static.size(1),
            dynamic_size=dynamic.size(1),
            hidden_size=128,
            num_satellites=3,
            rnn='indrnn',
            num_layers=2,
            constellation_mode='cooperative',
            use_transformer=False,
            update_fn=dataset.update_dynamic,
            mask_fn=dataset.update_mask
        ).to(device)
        
        # 预热
        with torch.no_grad():
            for _ in range(3):
                pn_actor(static, dynamic)
                gpn_actor(static, dynamic)
        
        # 测试PN模型时间
        pn_times = []
        for _ in range(10):
            start_time = time.time()
            with torch.no_grad():
                pn_actor(static, dynamic)
            pn_times.append(time.time() - start_time)
        
        # 测试GPN模型时间
        gpn_times = []
        for _ in range(10):
            start_time = time.time()
            with torch.no_grad():
                gpn_actor(static, dynamic)
            gpn_times.append(time.time() - start_time)
        
        pn_avg = np.mean(pn_times) * 1000  # 转换为毫秒
        gpn_avg = np.mean(gpn_times) * 1000
        
        print(f"前向传播时间对比:")
        print(f"  重构PN模型: {pn_avg:.2f}ms")
        print(f"  GPN模型: {gpn_avg:.2f}ms")
        print(f"  速度比: {pn_avg/gpn_avg:.2f}x")
        
        if pn_avg < gpn_avg * 2:  # 允许PN模型慢一些，但不能超过2倍
            print("✅ 计算效率测试通过")
            return True
        else:
            print("⚠️ 计算效率仍需优化")
            return False
            
    except Exception as e:
        print(f"❌ 效率测试失败: {e}")
        return False

def test_output_stability():
    """测试输出稳定性"""
    print("\n📊 测试输出稳定性")
    print("=" * 50)
    
    dataset = ConstellationSMPDataset(30, 10, 12345, 0.3, 5, 3)
    test_loader = DataLoader(dataset, batch_size=2, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    static = static.to(device)
    dynamic = dynamic.to(device)
    
    try:
        actor = PNConstellation(
            static_size=static.size(1),
            dynamic_size=dynamic.size(1),
            hidden_size=128,
            num_satellites=3,
            rnn='indrnn',
            num_layers=2,
            constellation_mode='cooperative',
            update_fn=dataset.update_dynamic,
            mask_fn=dataset.update_mask
        ).to(device)
        
        rewards = []
        revenue_rates = []
        
        for i in range(20):
            with torch.no_grad():
                tour_indices, satellite_indices, _, _ = actor(static, dynamic)
                reward_val, revenue_rate, _, _, _ = reward(static, tour_indices, satellite_indices, 'cooperative')
                rewards.append(reward_val.mean().item())
                revenue_rates.append(revenue_rate.mean().item())
        
        reward_std = np.std(rewards)
        revenue_std = np.std(revenue_rates)
        
        print(f"输出稳定性:")
        print(f"  奖励标准差: {reward_std:.4f}")
        print(f"  收益率标准差: {revenue_std:.4f}")
        print(f"  奖励范围: [{np.min(rewards):.3f}, {np.max(rewards):.3f}]")
        print(f"  收益率范围: [{np.min(revenue_rates):.3f}, {np.max(revenue_rates):.3f}]")
        
        if reward_std < 3.0 and revenue_std < 0.15:
            print("✅ 输出稳定性测试通过")
            return True
        else:
            print("⚠️ 输出稳定性需要改进")
            return False
            
    except Exception as e:
        print(f"❌ 稳定性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 PN+IndRNN模型重构验证 (方案B)")
    print("=" * 80)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("基本功能", test_basic_functionality()))
    test_results.append(("约束遵守", test_constraint_compliance()))
    test_results.append(("计算效率", test_computation_efficiency()))
    test_results.append(("输出稳定性", test_output_stability()))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📋 重构验证结果")
    print("=" * 80)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("🎉 方案B重构成功！PN+IndRNN模型已完全修复")
        return True
    elif passed >= len(test_results) * 0.75:
        print("⚠️ 重构基本成功，部分指标需要进一步优化")
        return True
    else:
        print("🔴 重构失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 建议：可以继续进行PN+IndRNN的消融实验")
    else:
        print("\n❌ 建议：需要进一步修复模型问题")
