#!/usr/bin/env python3
"""
测试修复后的日志系统
"""
import os
import sys
import tempfile

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_multi_constellation_modes import EnhancedTrainingLogger

def test_log_fix():
    """测试修复后的日志显示"""
    print("🧪 测试修复后的日志显示")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建日志记录器
        logger = EnhancedTrainingLogger('cooperative', 'test_model', temp_dir)
        
        # 模拟训练参数
        epoch = 0  # 第1个epoch (0-based)
        total_epochs = 3  # 总共3个epochs
        batch_idx = 9  # 第10个batch (0-based)
        total_batches = 100
        
        print(f"测试参数:")
        print(f"  当前epoch: {epoch} (显示为 {epoch+1})")
        print(f"  总epochs: {total_epochs}")
        print(f"  当前batch: {batch_idx} (显示为 {batch_idx+1})")
        print(f"  总batches: {total_batches}")
        print()
        
        # 测试修复后的日志记录
        print("修复后的日志输出:")
        logger.log_batch_info(
            epoch=epoch,
            batch_idx=batch_idx,
            total_batches=total_batches,
            loss=10.5,
            reward=12.3,
            critic_reward=11.8,
            revenue_rate=0.275,
            distance=4.5,
            memory=0.05,
            power=0.14,
            lr=0.0001,
            batch_time=3.5,
            total_epochs=total_epochs
        )
        
        print()
        print("✅ 日志显示修复测试完成!")
        print("现在epoch显示应该是正确的格式: Epoch 1/3")

if __name__ == '__main__':
    test_log_fix()
