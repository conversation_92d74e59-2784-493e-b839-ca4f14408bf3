训练日志 - GPN+LSTM+Transformer - COOPERATIVE 模式
================================================================================
开始时间: 2025-08-25 10:48:01
模型: GPN+LSTM+Transformer
星座模式: cooperative
================================================================================


[GPN+LSTM+Transformer-COOPERATIVE] 开始训练 Epoch 1/3
------------------------------------------------------------
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 10/3125, loss: 37.5172, reward: 10.2405, critic_reward: 15.6255, revenue_rate: 0.2664, distance: 4.3735, memory: 0.0941, power: 0.1389, lr: 0.000050, took: 26.125s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 20/3125, loss: 10.2665, reward: 10.4257, critic_reward: 12.3943, revenue_rate: 0.2695, distance: 4.3550, memory: 0.0554, power: 0.1319, lr: 0.000050, took: 20.415s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 30/3125, loss: 14.8089, reward: 11.2646, critic_reward: 7.8693, revenue_rate: 0.2926, distance: 4.7223, memory: 0.0477, power: 0.1445, lr: 0.000050, took: 21.720s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 40/3125, loss: 4.4812, reward: 10.2517, critic_reward: 9.9915, revenue_rate: 0.2633, distance: 4.3496, memory: 0.0711, power: 0.1322, lr: 0.000050, took: 21.613s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 50/3125, loss: 20.7654, reward: 8.9505, critic_reward: 12.9247, revenue_rate: 0.2347, distance: 3.8720, memory: 0.0636, power: 0.1135, lr: 0.000050, took: 20.545s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 60/3125, loss: 5.6560, reward: 11.9056, critic_reward: 11.1233, revenue_rate: 0.3107, distance: 5.1636, memory: 0.0269, power: 0.1484, lr: 0.000050, took: 20.924s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 70/3125, loss: 9.2974, reward: 11.0050, critic_reward: 13.2997, revenue_rate: 0.2846, distance: 4.6519, memory: 0.0283, power: 0.1347, lr: 0.000050, took: 20.459s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 80/3125, loss: 6.2305, reward: 8.8975, critic_reward: 10.7788, revenue_rate: 0.2357, distance: 3.9640, memory: 0.0607, power: 0.1241, lr: 0.000050, took: 20.416s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 90/3125, loss: 7.6498, reward: 10.3509, critic_reward: 8.6572, revenue_rate: 0.2702, distance: 4.3849, memory: 0.0811, power: 0.1323, lr: 0.000050, took: 21.093s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 100/3125, loss: 3.1002, reward: 9.8678, critic_reward: 10.3697, revenue_rate: 0.2566, distance: 4.2478, memory: 0.0167, power: 0.1249, lr: 0.000050, took: 21.075s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 110/3125, loss: 18.1843, reward: 10.5929, critic_reward: 6.8800, revenue_rate: 0.2678, distance: 4.1456, memory: 0.0134, power: 0.1302, lr: 0.000050, took: 20.482s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 120/3125, loss: 8.7685, reward: 11.7295, critic_reward: 9.5123, revenue_rate: 0.3030, distance: 4.7050, memory: 0.0451, power: 0.1534, lr: 0.000050, took: 21.099s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 130/3125, loss: 4.1559, reward: 11.8295, critic_reward: 12.0181, revenue_rate: 0.2987, distance: 4.8481, memory: 0.0294, power: 0.1447, lr: 0.000050, took: 20.900s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 140/3125, loss: 4.1203, reward: 10.8005, critic_reward: 9.4768, revenue_rate: 0.2723, distance: 4.3422, memory: 0.0400, power: 0.1355, lr: 0.000050, took: 20.985s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 150/3125, loss: 4.6212, reward: 9.3588, critic_reward: 7.8569, revenue_rate: 0.2390, distance: 3.8946, memory: 0.0537, power: 0.1153, lr: 0.000050, took: 20.906s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 160/3125, loss: 7.3596, reward: 10.1212, critic_reward: 12.1786, revenue_rate: 0.2604, distance: 4.0507, memory: 0.0608, power: 0.1336, lr: 0.000050, took: 20.683s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 170/3125, loss: 4.7830, reward: 9.6768, critic_reward: 8.3343, revenue_rate: 0.2531, distance: 4.1617, memory: 0.0198, power: 0.1275, lr: 0.000050, took: 20.955s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 180/3125, loss: 12.5042, reward: 11.2360, critic_reward: 14.4571, revenue_rate: 0.2909, distance: 4.5831, memory: 0.0563, power: 0.1387, lr: 0.000050, took: 20.502s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 190/3125, loss: 2.4095, reward: 11.6487, critic_reward: 11.9150, revenue_rate: 0.3028, distance: 4.8000, memory: 0.0639, power: 0.1449, lr: 0.000050, took: 21.431s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 200/3125, loss: 6.5519, reward: 9.6426, critic_reward: 11.5097, revenue_rate: 0.2488, distance: 4.0640, memory: 0.0329, power: 0.1241, lr: 0.000050, took: 23.688s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 210/3125, loss: 2.9320, reward: 11.3709, critic_reward: 11.6650, revenue_rate: 0.2978, distance: 4.7267, memory: 0.0505, power: 0.1435, lr: 0.000050, took: 20.771s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 220/3125, loss: 4.6823, reward: 11.2723, critic_reward: 12.4518, revenue_rate: 0.2886, distance: 4.5223, memory: 0.0462, power: 0.1430, lr: 0.000050, took: 21.009s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 230/3125, loss: 7.8522, reward: 11.3755, critic_reward: 10.0071, revenue_rate: 0.2907, distance: 4.4101, memory: -0.0348, power: 0.1362, lr: 0.000050, took: 21.628s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 240/3125, loss: 6.4504, reward: 9.7975, critic_reward: 8.0402, revenue_rate: 0.2494, distance: 3.8385, memory: 0.0111, power: 0.1242, lr: 0.000050, took: 21.930s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 250/3125, loss: 5.6005, reward: 10.0852, critic_reward: 11.8834, revenue_rate: 0.2615, distance: 4.2492, memory: 0.0634, power: 0.1295, lr: 0.000050, took: 21.501s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 260/3125, loss: 6.5873, reward: 9.3424, critic_reward: 11.3820, revenue_rate: 0.2424, distance: 4.0162, memory: 0.0587, power: 0.1216, lr: 0.000050, took: 19.902s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 270/3125, loss: 4.3425, reward: 9.8032, critic_reward: 10.8993, revenue_rate: 0.2524, distance: 4.2297, memory: 0.0236, power: 0.1215, lr: 0.000050, took: 20.181s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 280/3125, loss: 2.0217, reward: 10.2531, critic_reward: 10.7537, revenue_rate: 0.2720, distance: 4.5493, memory: 0.1028, power: 0.1416, lr: 0.000050, took: 21.568s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 290/3125, loss: 2.7293, reward: 10.0640, critic_reward: 9.9966, revenue_rate: 0.2667, distance: 4.5408, memory: 0.0545, power: 0.1330, lr: 0.000050, took: 20.952s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 300/3125, loss: 1.8818, reward: 10.3074, critic_reward: 10.6457, revenue_rate: 0.2666, distance: 4.3019, memory: 0.0179, power: 0.1280, lr: 0.000050, took: 19.902s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 310/3125, loss: 9.7310, reward: 11.0492, critic_reward: 13.6879, revenue_rate: 0.2827, distance: 4.4226, memory: 0.0119, power: 0.1359, lr: 0.000050, took: 21.127s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 320/3125, loss: 4.1116, reward: 10.9107, critic_reward: 10.0328, revenue_rate: 0.2786, distance: 4.3630, memory: 0.0223, power: 0.1370, lr: 0.000050, took: 21.310s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 330/3125, loss: 8.2624, reward: 12.2374, critic_reward: 10.3835, revenue_rate: 0.3184, distance: 5.0909, memory: -0.0198, power: 0.1478, lr: 0.000050, took: 20.684s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 340/3125, loss: 3.7773, reward: 11.2492, critic_reward: 11.6624, revenue_rate: 0.2892, distance: 4.6207, memory: 0.0632, power: 0.1428, lr: 0.000050, took: 21.668s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 350/3125, loss: 3.7129, reward: 10.6488, critic_reward: 11.0647, revenue_rate: 0.2754, distance: 4.3460, memory: 0.0323, power: 0.1304, lr: 0.000050, took: 20.311s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 360/3125, loss: 3.9364, reward: 11.5707, critic_reward: 11.3770, revenue_rate: 0.2985, distance: 4.6455, memory: -0.0070, power: 0.1388, lr: 0.000050, took: 20.350s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 370/3125, loss: 3.2472, reward: 11.2818, critic_reward: 10.2581, revenue_rate: 0.2895, distance: 4.5108, memory: 0.0343, power: 0.1414, lr: 0.000050, took: 21.263s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 380/3125, loss: 14.8734, reward: 10.4527, critic_reward: 7.1766, revenue_rate: 0.2675, distance: 4.2110, memory: 0.0359, power: 0.1290, lr: 0.000050, took: 20.232s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 390/3125, loss: 3.5245, reward: 9.1358, critic_reward: 10.0841, revenue_rate: 0.2378, distance: 4.0280, memory: 0.0754, power: 0.1187, lr: 0.000050, took: 20.307s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 400/3125, loss: 2.6304, reward: 11.4895, critic_reward: 10.8017, revenue_rate: 0.2915, distance: 4.3054, memory: 0.0066, power: 0.1403, lr: 0.000050, took: 20.515s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 410/3125, loss: 8.7571, reward: 12.0684, critic_reward: 9.6542, revenue_rate: 0.3170, distance: 5.1499, memory: 0.0690, power: 0.1567, lr: 0.000050, took: 21.078s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 420/3125, loss: 2.2979, reward: 9.8786, critic_reward: 9.2735, revenue_rate: 0.2540, distance: 4.0472, memory: 0.0364, power: 0.1279, lr: 0.000050, took: 21.303s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 430/3125, loss: 5.9618, reward: 11.0536, critic_reward: 9.7207, revenue_rate: 0.2888, distance: 4.4806, memory: 0.0069, power: 0.1365, lr: 0.000050, took: 20.647s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 440/3125, loss: 1.9562, reward: 10.0759, critic_reward: 10.0403, revenue_rate: 0.2589, distance: 4.0508, memory: 0.0329, power: 0.1208, lr: 0.000050, took: 20.334s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 450/3125, loss: 4.0593, reward: 9.3033, critic_reward: 7.9084, revenue_rate: 0.2472, distance: 4.2586, memory: 0.0538, power: 0.1248, lr: 0.000050, took: 19.346s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 460/3125, loss: 2.7125, reward: 10.8782, critic_reward: 11.1766, revenue_rate: 0.2806, distance: 4.5937, memory: 0.0240, power: 0.1259, lr: 0.000050, took: 21.388s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 470/3125, loss: 2.1640, reward: 11.1430, critic_reward: 11.2918, revenue_rate: 0.2885, distance: 4.5359, memory: 0.0245, power: 0.1348, lr: 0.000050, took: 21.584s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 480/3125, loss: 8.9601, reward: 11.4026, critic_reward: 8.9975, revenue_rate: 0.2915, distance: 4.6483, memory: 0.0207, power: 0.1383, lr: 0.000050, took: 21.073s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 490/3125, loss: 3.1335, reward: 11.1132, critic_reward: 11.3384, revenue_rate: 0.2844, distance: 4.5625, memory: 0.0429, power: 0.1370, lr: 0.000050, took: 21.454s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 500/3125, loss: 4.1610, reward: 11.3962, critic_reward: 10.7952, revenue_rate: 0.2997, distance: 5.1220, memory: 0.0560, power: 0.1546, lr: 0.000050, took: 20.901s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 510/3125, loss: 20.0969, reward: 13.4347, critic_reward: 9.5847, revenue_rate: 0.3489, distance: 5.4130, memory: 0.0118, power: 0.1684, lr: 0.000050, took: 21.364s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 520/3125, loss: 6.1879, reward: 11.7037, critic_reward: 9.9098, revenue_rate: 0.2970, distance: 4.7984, memory: 0.0579, power: 0.1488, lr: 0.000050, took: 20.415s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 530/3125, loss: 2.6236, reward: 10.4046, critic_reward: 11.0858, revenue_rate: 0.2690, distance: 4.4125, memory: 0.0421, power: 0.1299, lr: 0.000050, took: 21.365s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 540/3125, loss: 3.7714, reward: 10.2949, critic_reward: 11.1723, revenue_rate: 0.2664, distance: 4.2305, memory: 0.0549, power: 0.1310, lr: 0.000050, took: 20.381s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 550/3125, loss: 5.3585, reward: 11.3259, critic_reward: 12.2919, revenue_rate: 0.2898, distance: 4.6233, memory: 0.0386, power: 0.1426, lr: 0.000050, took: 21.556s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 560/3125, loss: 5.6459, reward: 10.6754, critic_reward: 8.9767, revenue_rate: 0.2779, distance: 4.6321, memory: 0.0582, power: 0.1401, lr: 0.000050, took: 21.662s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 570/3125, loss: 4.2358, reward: 11.6386, critic_reward: 10.7825, revenue_rate: 0.2995, distance: 4.6784, memory: 0.0031, power: 0.1475, lr: 0.000050, took: 21.618s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 580/3125, loss: 5.4072, reward: 10.6257, critic_reward: 9.5837, revenue_rate: 0.2815, distance: 4.5317, memory: 0.0080, power: 0.1337, lr: 0.000050, took: 20.738s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 590/3125, loss: 5.0546, reward: 11.2550, critic_reward: 9.4917, revenue_rate: 0.2904, distance: 4.6362, memory: 0.0437, power: 0.1413, lr: 0.000050, took: 20.602s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 600/3125, loss: 3.5908, reward: 11.0392, critic_reward: 11.0220, revenue_rate: 0.2780, distance: 4.0150, memory: -0.0096, power: 0.1286, lr: 0.000050, took: 20.670s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 610/3125, loss: 2.5568, reward: 9.9237, critic_reward: 10.4322, revenue_rate: 0.2558, distance: 4.0859, memory: 0.0571, power: 0.1221, lr: 0.000050, took: 21.596s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 620/3125, loss: 4.3120, reward: 9.6925, critic_reward: 8.9746, revenue_rate: 0.2562, distance: 4.1067, memory: 0.0237, power: 0.1220, lr: 0.000050, took: 21.145s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 630/3125, loss: 2.6424, reward: 10.5385, critic_reward: 10.1085, revenue_rate: 0.2743, distance: 4.4948, memory: 0.0515, power: 0.1330, lr: 0.000050, took: 22.801s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 640/3125, loss: 2.9683, reward: 11.1649, critic_reward: 11.6862, revenue_rate: 0.2886, distance: 4.7100, memory: 0.0328, power: 0.1398, lr: 0.000050, took: 21.187s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 650/3125, loss: 3.8692, reward: 10.4832, critic_reward: 10.5888, revenue_rate: 0.2699, distance: 4.2466, memory: 0.0260, power: 0.1299, lr: 0.000050, took: 21.147s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 660/3125, loss: 3.4524, reward: 9.6833, critic_reward: 10.2187, revenue_rate: 0.2486, distance: 3.9496, memory: 0.0248, power: 0.1151, lr: 0.000050, took: 21.365s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 670/3125, loss: 3.8238, reward: 11.3491, critic_reward: 12.5137, revenue_rate: 0.2888, distance: 4.8017, memory: 0.0492, power: 0.1447, lr: 0.000050, took: 21.241s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 680/3125, loss: 3.0068, reward: 10.4940, critic_reward: 9.7931, revenue_rate: 0.2677, distance: 4.2607, memory: 0.0502, power: 0.1261, lr: 0.000050, took: 20.770s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 690/3125, loss: 4.1933, reward: 9.8657, critic_reward: 8.7139, revenue_rate: 0.2512, distance: 3.8512, memory: 0.0439, power: 0.1235, lr: 0.000050, took: 20.569s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 700/3125, loss: 3.4705, reward: 10.4891, critic_reward: 11.4644, revenue_rate: 0.2743, distance: 4.4475, memory: 0.0592, power: 0.1338, lr: 0.000050, took: 20.619s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 710/3125, loss: 2.1457, reward: 10.2310, critic_reward: 10.1161, revenue_rate: 0.2659, distance: 4.3204, memory: 0.0079, power: 0.1284, lr: 0.000050, took: 20.062s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 720/3125, loss: 5.3530, reward: 10.5261, critic_reward: 9.0225, revenue_rate: 0.2756, distance: 4.6217, memory: 0.0287, power: 0.1350, lr: 0.000050, took: 19.565s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 730/3125, loss: 2.3767, reward: 9.8691, critic_reward: 10.4212, revenue_rate: 0.2543, distance: 4.1333, memory: 0.0267, power: 0.1267, lr: 0.000050, took: 20.332s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 740/3125, loss: 1.8962, reward: 10.9517, critic_reward: 10.4279, revenue_rate: 0.2740, distance: 4.1519, memory: 0.0187, power: 0.1322, lr: 0.000050, took: 20.743s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 750/3125, loss: 11.6360, reward: 11.3259, critic_reward: 8.2276, revenue_rate: 0.2924, distance: 4.9285, memory: 0.0822, power: 0.1436, lr: 0.000050, took: 19.790s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 760/3125, loss: 4.7426, reward: 11.7345, critic_reward: 11.4750, revenue_rate: 0.2977, distance: 4.7898, memory: 0.0002, power: 0.1411, lr: 0.000050, took: 21.694s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 770/3125, loss: 1.8192, reward: 8.8221, critic_reward: 9.4551, revenue_rate: 0.2361, distance: 4.1521, memory: 0.0737, power: 0.1176, lr: 0.000050, took: 20.409s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 780/3125, loss: 2.4957, reward: 9.9393, critic_reward: 10.7735, revenue_rate: 0.2587, distance: 4.4281, memory: 0.0552, power: 0.1256, lr: 0.000050, took: 21.287s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 790/3125, loss: 8.4598, reward: 9.6962, critic_reward: 12.3596, revenue_rate: 0.2520, distance: 4.0835, memory: 0.0396, power: 0.1221, lr: 0.000050, took: 21.609s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 800/3125, loss: 5.6692, reward: 11.9785, critic_reward: 10.9694, revenue_rate: 0.3033, distance: 4.9903, memory: -0.0013, power: 0.1477, lr: 0.000050, took: 20.788s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 810/3125, loss: 1.9350, reward: 10.4218, critic_reward: 10.8965, revenue_rate: 0.2641, distance: 4.1232, memory: -0.0190, power: 0.1273, lr: 0.000050, took: 21.186s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 820/3125, loss: 10.1341, reward: 13.2444, critic_reward: 11.0334, revenue_rate: 0.3297, distance: 5.0055, memory: -0.0177, power: 0.1546, lr: 0.000050, took: 21.711s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 830/3125, loss: 2.9439, reward: 10.9819, critic_reward: 9.9057, revenue_rate: 0.2798, distance: 4.2293, memory: 0.0334, power: 0.1360, lr: 0.000050, took: 22.369s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 840/3125, loss: 3.0112, reward: 10.6553, critic_reward: 10.4549, revenue_rate: 0.2702, distance: 4.3099, memory: -0.0151, power: 0.1320, lr: 0.000050, took: 20.820s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 850/3125, loss: 3.0125, reward: 10.4007, critic_reward: 11.1449, revenue_rate: 0.2703, distance: 4.5771, memory: 0.0441, power: 0.1337, lr: 0.000050, took: 20.432s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 860/3125, loss: 2.9583, reward: 11.2690, critic_reward: 10.7903, revenue_rate: 0.2838, distance: 4.2445, memory: 0.0139, power: 0.1349, lr: 0.000050, took: 20.816s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 870/3125, loss: 6.5993, reward: 10.5397, critic_reward: 8.4999, revenue_rate: 0.2782, distance: 4.8199, memory: 0.1016, power: 0.1427, lr: 0.000050, took: 20.915s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 880/3125, loss: 2.1171, reward: 10.0096, critic_reward: 10.3322, revenue_rate: 0.2613, distance: 4.4042, memory: 0.0563, power: 0.1265, lr: 0.000050, took: 21.412s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 890/3125, loss: 3.1387, reward: 10.5184, critic_reward: 11.5525, revenue_rate: 0.2740, distance: 4.4435, memory: 0.0440, power: 0.1320, lr: 0.000050, took: 21.128s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 900/3125, loss: 7.2452, reward: 12.7881, critic_reward: 11.0454, revenue_rate: 0.3275, distance: 5.2108, memory: 0.0025, power: 0.1541, lr: 0.000050, took: 20.015s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 910/3125, loss: 6.3674, reward: 11.7733, critic_reward: 9.6063, revenue_rate: 0.2980, distance: 4.7279, memory: 0.0357, power: 0.1462, lr: 0.000050, took: 20.805s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 920/3125, loss: 3.2605, reward: 10.5420, critic_reward: 11.2958, revenue_rate: 0.2744, distance: 4.4316, memory: 0.0484, power: 0.1361, lr: 0.000050, took: 20.914s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 930/3125, loss: 3.5361, reward: 11.7107, critic_reward: 10.9476, revenue_rate: 0.3032, distance: 4.9940, memory: 0.0653, power: 0.1505, lr: 0.000050, took: 21.678s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 940/3125, loss: 2.0844, reward: 9.8314, critic_reward: 10.1368, revenue_rate: 0.2547, distance: 4.0399, memory: 0.0380, power: 0.1219, lr: 0.000050, took: 20.994s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 950/3125, loss: 4.0117, reward: 11.5915, critic_reward: 11.2533, revenue_rate: 0.3021, distance: 4.9455, memory: 0.0294, power: 0.1484, lr: 0.000050, took: 21.170s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 960/3125, loss: 9.0098, reward: 12.9742, critic_reward: 10.4467, revenue_rate: 0.3405, distance: 5.5121, memory: 0.0453, power: 0.1693, lr: 0.000050, took: 21.220s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 970/3125, loss: 4.6214, reward: 11.9913, critic_reward: 11.3543, revenue_rate: 0.3035, distance: 4.6744, memory: 0.0241, power: 0.1377, lr: 0.000050, took: 21.366s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 980/3125, loss: 4.0006, reward: 11.2569, critic_reward: 11.2239, revenue_rate: 0.2933, distance: 4.8087, memory: -0.0112, power: 0.1450, lr: 0.000050, took: 21.409s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 990/3125, loss: 2.7761, reward: 10.9394, critic_reward: 11.0673, revenue_rate: 0.2792, distance: 4.3827, memory: 0.0433, power: 0.1327, lr: 0.000050, took: 20.982s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1000/3125, loss: 4.4658, reward: 10.0975, critic_reward: 11.4725, revenue_rate: 0.2575, distance: 4.0632, memory: 0.0531, power: 0.1262, lr: 0.000050, took: 20.940s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1010/3125, loss: 6.9739, reward: 11.5364, critic_reward: 13.2925, revenue_rate: 0.2951, distance: 4.7083, memory: 0.0474, power: 0.1403, lr: 0.000050, took: 20.831s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1020/3125, loss: 5.0135, reward: 11.2016, critic_reward: 9.8215, revenue_rate: 0.2879, distance: 4.7227, memory: 0.0380, power: 0.1361, lr: 0.000050, took: 21.197s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1030/3125, loss: 0.9825, reward: 10.3708, critic_reward: 10.2044, revenue_rate: 0.2590, distance: 4.0663, memory: 0.0378, power: 0.1230, lr: 0.000050, took: 20.996s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1040/3125, loss: 2.0123, reward: 11.4551, critic_reward: 11.5414, revenue_rate: 0.2954, distance: 4.7972, memory: 0.0348, power: 0.1391, lr: 0.000050, took: 21.457s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1050/3125, loss: 5.1395, reward: 12.5465, critic_reward: 11.1462, revenue_rate: 0.3250, distance: 5.4054, memory: 0.0251, power: 0.1577, lr: 0.000050, took: 23.771s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1060/3125, loss: 5.2667, reward: 9.3582, critic_reward: 11.1570, revenue_rate: 0.2396, distance: 3.8092, memory: 0.0442, power: 0.1176, lr: 0.000050, took: 20.645s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1070/3125, loss: 4.5913, reward: 11.1923, critic_reward: 10.5802, revenue_rate: 0.2867, distance: 4.6981, memory: 0.0323, power: 0.1412, lr: 0.000050, took: 20.742s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1080/3125, loss: 1.6032, reward: 10.6422, critic_reward: 10.0498, revenue_rate: 0.2750, distance: 4.5336, memory: 0.0218, power: 0.1352, lr: 0.000050, took: 21.083s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1090/3125, loss: 8.3717, reward: 13.1571, critic_reward: 10.7262, revenue_rate: 0.3365, distance: 5.4261, memory: 0.0079, power: 0.1643, lr: 0.000050, took: 21.470s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1100/3125, loss: 4.1478, reward: 9.7855, critic_reward: 10.9996, revenue_rate: 0.2565, distance: 4.1838, memory: 0.0489, power: 0.1276, lr: 0.000050, took: 20.989s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1110/3125, loss: 1.7611, reward: 10.3187, critic_reward: 10.3159, revenue_rate: 0.2760, distance: 4.6275, memory: 0.0839, power: 0.1387, lr: 0.000050, took: 20.836s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1120/3125, loss: 5.4373, reward: 9.9015, critic_reward: 11.6010, revenue_rate: 0.2614, distance: 4.4714, memory: 0.0819, power: 0.1374, lr: 0.000050, took: 20.678s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1130/3125, loss: 1.9922, reward: 10.5540, critic_reward: 10.2022, revenue_rate: 0.2739, distance: 4.3055, memory: 0.0590, power: 0.1337, lr: 0.000050, took: 20.841s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1140/3125, loss: 8.3706, reward: 11.2640, critic_reward: 8.7133, revenue_rate: 0.2966, distance: 5.1241, memory: 0.1116, power: 0.1488, lr: 0.000050, took: 21.628s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1150/3125, loss: 15.2255, reward: 10.3666, critic_reward: 13.9627, revenue_rate: 0.2633, distance: 4.3257, memory: 0.0307, power: 0.1292, lr: 0.000050, took: 20.979s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1160/3125, loss: 4.0296, reward: 9.7923, critic_reward: 8.5549, revenue_rate: 0.2517, distance: 4.1164, memory: 0.0318, power: 0.1297, lr: 0.000050, took: 20.272s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1170/3125, loss: 5.1221, reward: 10.9107, critic_reward: 9.6572, revenue_rate: 0.2837, distance: 4.6824, memory: 0.0231, power: 0.1355, lr: 0.000050, took: 19.981s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1180/3125, loss: 10.5856, reward: 10.8644, critic_reward: 13.6867, revenue_rate: 0.2782, distance: 4.5858, memory: 0.0612, power: 0.1367, lr: 0.000050, took: 21.219s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1190/3125, loss: 13.0694, reward: 11.1644, critic_reward: 8.0797, revenue_rate: 0.2855, distance: 4.6810, memory: 0.0052, power: 0.1352, lr: 0.000050, took: 20.873s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1200/3125, loss: 2.5403, reward: 10.4761, critic_reward: 10.7597, revenue_rate: 0.2736, distance: 4.3829, memory: 0.0626, power: 0.1335, lr: 0.000050, took: 20.908s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1210/3125, loss: 2.5174, reward: 10.1795, critic_reward: 9.4550, revenue_rate: 0.2602, distance: 4.2339, memory: -0.0018, power: 0.1213, lr: 0.000050, took: 20.779s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1220/3125, loss: 4.1063, reward: 11.0309, critic_reward: 9.4491, revenue_rate: 0.2837, distance: 4.6949, memory: 0.0646, power: 0.1411, lr: 0.000050, took: 20.599s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1230/3125, loss: 2.9737, reward: 10.1355, critic_reward: 10.3505, revenue_rate: 0.2659, distance: 4.3135, memory: 0.0338, power: 0.1256, lr: 0.000050, took: 20.963s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1240/3125, loss: 4.1455, reward: 10.7340, critic_reward: 9.5120, revenue_rate: 0.2793, distance: 4.4822, memory: 0.0534, power: 0.1400, lr: 0.000050, took: 21.041s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1250/3125, loss: 3.0096, reward: 10.6725, critic_reward: 10.6649, revenue_rate: 0.2694, distance: 4.1921, memory: 0.0327, power: 0.1302, lr: 0.000050, took: 20.582s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1260/3125, loss: 8.1424, reward: 12.8597, critic_reward: 10.5661, revenue_rate: 0.3331, distance: 5.3820, memory: 0.0531, power: 0.1643, lr: 0.000050, took: 20.740s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1270/3125, loss: 4.0664, reward: 11.8610, critic_reward: 10.7300, revenue_rate: 0.3058, distance: 4.7688, memory: 0.0577, power: 0.1515, lr: 0.000050, took: 20.782s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1280/3125, loss: 2.2866, reward: 10.2582, critic_reward: 10.3223, revenue_rate: 0.2690, distance: 4.5189, memory: 0.0595, power: 0.1365, lr: 0.000050, took: 20.669s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1290/3125, loss: 3.1228, reward: 11.1232, critic_reward: 11.1735, revenue_rate: 0.2865, distance: 4.5946, memory: 0.0124, power: 0.1403, lr: 0.000050, took: 20.873s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1300/3125, loss: 7.8206, reward: 10.1041, critic_reward: 12.5474, revenue_rate: 0.2611, distance: 4.0631, memory: 0.0622, power: 0.1290, lr: 0.000050, took: 21.002s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1310/3125, loss: 2.7157, reward: 10.2252, critic_reward: 11.4355, revenue_rate: 0.2692, distance: 4.3267, memory: 0.0996, power: 0.1357, lr: 0.000050, took: 21.635s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1320/3125, loss: 2.0218, reward: 10.0771, critic_reward: 9.2518, revenue_rate: 0.2652, distance: 4.3558, memory: 0.0302, power: 0.1285, lr: 0.000050, took: 20.443s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1330/3125, loss: 4.3489, reward: 10.0424, critic_reward: 11.4682, revenue_rate: 0.2566, distance: 4.2726, memory: 0.0516, power: 0.1284, lr: 0.000050, took: 20.336s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1340/3125, loss: 2.5465, reward: 10.4061, critic_reward: 10.8530, revenue_rate: 0.2647, distance: 4.2026, memory: 0.0654, power: 0.1323, lr: 0.000050, took: 20.999s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1350/3125, loss: 3.1570, reward: 10.9085, critic_reward: 11.8243, revenue_rate: 0.2767, distance: 4.1269, memory: 0.0498, power: 0.1375, lr: 0.000050, took: 21.580s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1360/3125, loss: 2.2073, reward: 10.6569, critic_reward: 10.2915, revenue_rate: 0.2791, distance: 4.3545, memory: 0.0409, power: 0.1345, lr: 0.000050, took: 20.402s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1370/3125, loss: 5.9829, reward: 10.1711, critic_reward: 11.9860, revenue_rate: 0.2628, distance: 4.3079, memory: 0.0097, power: 0.1215, lr: 0.000050, took: 20.831s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1380/3125, loss: 3.7597, reward: 10.3047, critic_reward: 11.3044, revenue_rate: 0.2624, distance: 4.1808, memory: 0.0461, power: 0.1263, lr: 0.000050, took: 20.784s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1390/3125, loss: 3.8694, reward: 11.0109, critic_reward: 9.6128, revenue_rate: 0.2810, distance: 4.5811, memory: 0.0378, power: 0.1406, lr: 0.000050, took: 20.414s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1400/3125, loss: 1.4000, reward: 10.8557, critic_reward: 10.8103, revenue_rate: 0.2793, distance: 4.6199, memory: 0.0698, power: 0.1364, lr: 0.000050, took: 20.554s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1410/3125, loss: 11.0068, reward: 11.6381, critic_reward: 8.9517, revenue_rate: 0.2961, distance: 4.5883, memory: -0.0190, power: 0.1420, lr: 0.000050, took: 20.711s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1420/3125, loss: 4.7771, reward: 9.8123, critic_reward: 11.5728, revenue_rate: 0.2487, distance: 3.9452, memory: 0.0758, power: 0.1230, lr: 0.000050, took: 19.583s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1430/3125, loss: 6.2062, reward: 9.5183, critic_reward: 7.4297, revenue_rate: 0.2507, distance: 4.2921, memory: 0.0939, power: 0.1274, lr: 0.000050, took: 19.680s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1440/3125, loss: 4.3383, reward: 11.7811, critic_reward: 12.1859, revenue_rate: 0.2979, distance: 4.5811, memory: -0.0203, power: 0.1394, lr: 0.000050, took: 20.057s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1450/3125, loss: 3.1378, reward: 10.3963, critic_reward: 10.8600, revenue_rate: 0.2683, distance: 4.3191, memory: 0.0205, power: 0.1303, lr: 0.000050, took: 21.204s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1460/3125, loss: 2.1361, reward: 10.6839, critic_reward: 10.9323, revenue_rate: 0.2779, distance: 4.5618, memory: 0.0848, power: 0.1356, lr: 0.000050, took: 21.055s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1470/3125, loss: 4.1698, reward: 10.2629, critic_reward: 11.2191, revenue_rate: 0.2662, distance: 4.3368, memory: 0.0113, power: 0.1261, lr: 0.000050, took: 20.701s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1480/3125, loss: 6.6335, reward: 8.9203, critic_reward: 11.1039, revenue_rate: 0.2366, distance: 4.2190, memory: 0.0432, power: 0.1222, lr: 0.000050, took: 21.341s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1490/3125, loss: 2.3078, reward: 10.9092, critic_reward: 10.0833, revenue_rate: 0.2873, distance: 4.8969, memory: 0.0529, power: 0.1505, lr: 0.000050, took: 23.009s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1500/3125, loss: 2.7825, reward: 10.5706, critic_reward: 11.3422, revenue_rate: 0.2743, distance: 4.4823, memory: 0.0299, power: 0.1279, lr: 0.000050, took: 21.520s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1510/3125, loss: 2.1309, reward: 10.0637, critic_reward: 9.6579, revenue_rate: 0.2641, distance: 4.4500, memory: 0.0307, power: 0.1294, lr: 0.000050, took: 19.921s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1520/3125, loss: 2.4566, reward: 10.2658, critic_reward: 10.5741, revenue_rate: 0.2693, distance: 4.4267, memory: 0.0429, power: 0.1301, lr: 0.000050, took: 20.703s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1530/3125, loss: 3.1917, reward: 10.6721, critic_reward: 11.1895, revenue_rate: 0.2691, distance: 4.4246, memory: 0.0481, power: 0.1394, lr: 0.000050, took: 21.162s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1540/3125, loss: 4.9346, reward: 10.7302, critic_reward: 9.0775, revenue_rate: 0.2730, distance: 4.2248, memory: -0.0095, power: 0.1305, lr: 0.000050, took: 20.694s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1550/3125, loss: 8.1534, reward: 10.1874, critic_reward: 12.6209, revenue_rate: 0.2625, distance: 4.2912, memory: 0.0617, power: 0.1286, lr: 0.000050, took: 20.953s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1560/3125, loss: 4.2119, reward: 10.5098, critic_reward: 8.9996, revenue_rate: 0.2686, distance: 4.2286, memory: 0.0139, power: 0.1317, lr: 0.000050, took: 20.806s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1570/3125, loss: 5.8207, reward: 11.0417, critic_reward: 12.4461, revenue_rate: 0.2885, distance: 4.6754, memory: 0.0551, power: 0.1424, lr: 0.000050, took: 21.753s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1580/3125, loss: 1.6125, reward: 9.6917, critic_reward: 10.3857, revenue_rate: 0.2519, distance: 4.0623, memory: 0.0712, power: 0.1269, lr: 0.000050, took: 20.980s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1590/3125, loss: 4.7680, reward: 9.6293, critic_reward: 11.0147, revenue_rate: 0.2535, distance: 4.1734, memory: 0.0230, power: 0.1263, lr: 0.000050, took: 20.972s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1600/3125, loss: 2.2915, reward: 10.3350, critic_reward: 9.8655, revenue_rate: 0.2610, distance: 4.1780, memory: 0.0171, power: 0.1225, lr: 0.000050, took: 20.075s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1610/3125, loss: 4.6820, reward: 10.1244, critic_reward: 10.9994, revenue_rate: 0.2671, distance: 4.3891, memory: 0.0306, power: 0.1377, lr: 0.000050, took: 21.906s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1620/3125, loss: 2.2767, reward: 9.9192, critic_reward: 10.4704, revenue_rate: 0.2586, distance: 4.4083, memory: 0.0660, power: 0.1248, lr: 0.000050, took: 20.538s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1630/3125, loss: 3.4435, reward: 11.9460, critic_reward: 12.2431, revenue_rate: 0.3020, distance: 4.5829, memory: -0.0151, power: 0.1525, lr: 0.000050, took: 22.530s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1640/3125, loss: 2.6233, reward: 10.2358, critic_reward: 11.0432, revenue_rate: 0.2627, distance: 4.2113, memory: 0.0741, power: 0.1305, lr: 0.000050, took: 20.730s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1650/3125, loss: 3.3393, reward: 11.0933, critic_reward: 10.0487, revenue_rate: 0.2864, distance: 4.5235, memory: 0.0294, power: 0.1356, lr: 0.000050, took: 21.621s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1660/3125, loss: 2.9038, reward: 10.9778, critic_reward: 11.2730, revenue_rate: 0.2806, distance: 4.4530, memory: 0.0426, power: 0.1364, lr: 0.000050, took: 20.807s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1670/3125, loss: 3.1389, reward: 10.4666, critic_reward: 9.9262, revenue_rate: 0.2673, distance: 4.2517, memory: 0.0232, power: 0.1233, lr: 0.000050, took: 21.080s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1680/3125, loss: 2.3685, reward: 11.5088, critic_reward: 10.6960, revenue_rate: 0.2922, distance: 4.4999, memory: 0.0062, power: 0.1325, lr: 0.000050, took: 20.519s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1690/3125, loss: 10.3238, reward: 11.2227, critic_reward: 8.5321, revenue_rate: 0.2904, distance: 4.7512, memory: 0.0686, power: 0.1411, lr: 0.000050, took: 20.123s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1700/3125, loss: 4.8550, reward: 11.0877, critic_reward: 11.5211, revenue_rate: 0.2832, distance: 4.4361, memory: -0.0167, power: 0.1364, lr: 0.000050, took: 20.244s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1710/3125, loss: 3.7061, reward: 10.5304, critic_reward: 9.7776, revenue_rate: 0.2837, distance: 5.1274, memory: 0.0785, power: 0.1457, lr: 0.000050, took: 21.559s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1720/3125, loss: 3.3306, reward: 10.6735, critic_reward: 11.9004, revenue_rate: 0.2780, distance: 4.4048, memory: 0.0193, power: 0.1351, lr: 0.000050, took: 21.094s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1730/3125, loss: 2.3804, reward: 10.5868, critic_reward: 10.2359, revenue_rate: 0.2721, distance: 4.0773, memory: 0.0278, power: 0.1337, lr: 0.000050, took: 20.967s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1740/3125, loss: 3.5572, reward: 10.9302, critic_reward: 10.4827, revenue_rate: 0.2845, distance: 4.5554, memory: 0.0203, power: 0.1452, lr: 0.000050, took: 20.994s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1750/3125, loss: 5.0703, reward: 12.2654, critic_reward: 11.6276, revenue_rate: 0.3088, distance: 4.9209, memory: 0.0545, power: 0.1501, lr: 0.000050, took: 21.583s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1760/3125, loss: 1.5817, reward: 11.4130, critic_reward: 11.1423, revenue_rate: 0.2951, distance: 4.6034, memory: 0.0548, power: 0.1470, lr: 0.000050, took: 20.670s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1770/3125, loss: 2.3942, reward: 10.6858, critic_reward: 10.1266, revenue_rate: 0.2733, distance: 4.2599, memory: 0.0593, power: 0.1342, lr: 0.000050, took: 21.290s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1780/3125, loss: 5.9707, reward: 10.3137, critic_reward: 11.8102, revenue_rate: 0.2648, distance: 4.1806, memory: -0.0034, power: 0.1267, lr: 0.000050, took: 20.336s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1790/3125, loss: 3.6879, reward: 11.1246, critic_reward: 10.2858, revenue_rate: 0.2895, distance: 4.7292, memory: 0.0491, power: 0.1474, lr: 0.000050, took: 20.204s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1800/3125, loss: 1.6450, reward: 10.8273, critic_reward: 10.4660, revenue_rate: 0.2775, distance: 4.2694, memory: 0.0299, power: 0.1335, lr: 0.000050, took: 20.506s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1810/3125, loss: 2.3903, reward: 11.6225, critic_reward: 10.8936, revenue_rate: 0.3048, distance: 5.0185, memory: 0.0842, power: 0.1522, lr: 0.000050, took: 21.726s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1820/3125, loss: 4.7518, reward: 10.5602, critic_reward: 10.9254, revenue_rate: 0.2745, distance: 4.3808, memory: -0.0054, power: 0.1293, lr: 0.000050, took: 21.008s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1830/3125, loss: 1.9459, reward: 9.9392, critic_reward: 10.3371, revenue_rate: 0.2541, distance: 3.9681, memory: 0.0390, power: 0.1271, lr: 0.000050, took: 20.376s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1840/3125, loss: 4.4251, reward: 11.4197, critic_reward: 13.0270, revenue_rate: 0.2944, distance: 4.7391, memory: -0.0034, power: 0.1374, lr: 0.000050, took: 22.072s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1850/3125, loss: 5.1521, reward: 10.5472, critic_reward: 9.4462, revenue_rate: 0.2671, distance: 4.2673, memory: 0.0202, power: 0.1305, lr: 0.000050, took: 20.494s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1860/3125, loss: 2.3047, reward: 10.9410, critic_reward: 11.7585, revenue_rate: 0.2829, distance: 4.6179, memory: 0.0342, power: 0.1408, lr: 0.000050, took: 21.444s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1870/3125, loss: 2.7006, reward: 11.2394, critic_reward: 10.9187, revenue_rate: 0.2813, distance: 4.2823, memory: 0.0219, power: 0.1358, lr: 0.000050, took: 21.153s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1880/3125, loss: 7.5454, reward: 11.7336, critic_reward: 9.6915, revenue_rate: 0.3002, distance: 4.6919, memory: 0.0407, power: 0.1502, lr: 0.000050, took: 20.647s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1890/3125, loss: 8.8174, reward: 10.9120, critic_reward: 13.1524, revenue_rate: 0.2789, distance: 4.6563, memory: 0.0561, power: 0.1448, lr: 0.000050, took: 21.742s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1900/3125, loss: 7.1966, reward: 12.3359, critic_reward: 10.4635, revenue_rate: 0.3209, distance: 5.2220, memory: 0.0440, power: 0.1559, lr: 0.000050, took: 21.328s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1910/3125, loss: 5.0940, reward: 13.1477, critic_reward: 12.0755, revenue_rate: 0.3336, distance: 5.3061, memory: 0.0308, power: 0.1615, lr: 0.000050, took: 24.334s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1920/3125, loss: 4.6794, reward: 11.0990, critic_reward: 9.5695, revenue_rate: 0.2825, distance: 4.5477, memory: 0.0257, power: 0.1410, lr: 0.000050, took: 20.390s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1930/3125, loss: 2.9186, reward: 11.5350, critic_reward: 10.8556, revenue_rate: 0.2897, distance: 4.4589, memory: 0.0567, power: 0.1457, lr: 0.000050, took: 20.109s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1940/3125, loss: 3.3734, reward: 10.9241, critic_reward: 11.4676, revenue_rate: 0.2842, distance: 4.7688, memory: 0.0366, power: 0.1464, lr: 0.000050, took: 21.244s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1950/3125, loss: 1.1975, reward: 10.2937, critic_reward: 10.3630, revenue_rate: 0.2611, distance: 4.0914, memory: 0.0726, power: 0.1298, lr: 0.000050, took: 20.557s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1960/3125, loss: 4.1389, reward: 10.9579, critic_reward: 11.8887, revenue_rate: 0.2748, distance: 4.3920, memory: 0.0103, power: 0.1382, lr: 0.000050, took: 21.016s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1970/3125, loss: 3.4629, reward: 10.7737, critic_reward: 10.7690, revenue_rate: 0.2748, distance: 4.2717, memory: 0.0534, power: 0.1338, lr: 0.000050, took: 20.971s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1980/3125, loss: 11.9510, reward: 13.0411, critic_reward: 10.1756, revenue_rate: 0.3347, distance: 5.6200, memory: 0.0235, power: 0.1616, lr: 0.000050, took: 21.294s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 1990/3125, loss: 3.0658, reward: 10.4967, critic_reward: 9.4511, revenue_rate: 0.2739, distance: 4.5394, memory: 0.0341, power: 0.1331, lr: 0.000050, took: 20.032s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2000/3125, loss: 6.2082, reward: 13.5294, critic_reward: 11.7343, revenue_rate: 0.3456, distance: 5.2667, memory: 0.0520, power: 0.1663, lr: 0.000050, took: 21.008s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2010/3125, loss: 3.3305, reward: 10.7134, critic_reward: 11.7178, revenue_rate: 0.2836, distance: 4.7961, memory: 0.1032, power: 0.1427, lr: 0.000050, took: 21.780s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2020/3125, loss: 5.4691, reward: 11.5001, critic_reward: 9.8973, revenue_rate: 0.2976, distance: 4.8048, memory: 0.0656, power: 0.1544, lr: 0.000050, took: 21.679s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2030/3125, loss: 2.5269, reward: 9.7081, critic_reward: 9.1395, revenue_rate: 0.2482, distance: 4.0936, memory: 0.0332, power: 0.1257, lr: 0.000050, took: 19.939s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2040/3125, loss: 3.5297, reward: 10.5857, critic_reward: 11.6045, revenue_rate: 0.2708, distance: 4.2991, memory: 0.0212, power: 0.1335, lr: 0.000050, took: 21.286s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2050/3125, loss: 2.7825, reward: 10.2570, critic_reward: 9.5581, revenue_rate: 0.2740, distance: 4.4398, memory: 0.0495, power: 0.1280, lr: 0.000050, took: 21.585s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2060/3125, loss: 3.2809, reward: 10.1746, critic_reward: 11.4063, revenue_rate: 0.2640, distance: 3.9913, memory: 0.0225, power: 0.1308, lr: 0.000050, took: 23.064s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2070/3125, loss: 4.1037, reward: 9.6996, critic_reward: 11.0929, revenue_rate: 0.2508, distance: 4.1665, memory: 0.0391, power: 0.1265, lr: 0.000050, took: 24.529s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2080/3125, loss: 2.3687, reward: 10.5936, critic_reward: 9.8112, revenue_rate: 0.2679, distance: 3.9478, memory: -0.0348, power: 0.1248, lr: 0.000050, took: 22.750s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2090/3125, loss: 6.5012, reward: 12.0900, critic_reward: 10.4421, revenue_rate: 0.3169, distance: 4.9038, memory: 0.0469, power: 0.1442, lr: 0.000050, took: 22.598s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2100/3125, loss: 4.1924, reward: 9.4610, critic_reward: 10.8768, revenue_rate: 0.2476, distance: 4.0972, memory: 0.0058, power: 0.1188, lr: 0.000050, took: 20.599s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2110/3125, loss: 3.4072, reward: 11.8018, critic_reward: 11.2198, revenue_rate: 0.3006, distance: 4.6672, memory: 0.0403, power: 0.1514, lr: 0.000050, took: 22.288s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2120/3125, loss: 8.6946, reward: 12.7771, critic_reward: 10.4532, revenue_rate: 0.3241, distance: 4.9358, memory: -0.0080, power: 0.1555, lr: 0.000050, took: 22.281s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2130/3125, loss: 4.0135, reward: 12.1187, critic_reward: 11.3730, revenue_rate: 0.3112, distance: 5.2482, memory: 0.0673, power: 0.1533, lr: 0.000050, took: 21.119s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2140/3125, loss: 5.7189, reward: 11.4294, critic_reward: 10.6215, revenue_rate: 0.2983, distance: 4.8916, memory: 0.0202, power: 0.1419, lr: 0.000050, took: 20.671s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2150/3125, loss: 3.2610, reward: 10.8226, critic_reward: 11.1878, revenue_rate: 0.2751, distance: 4.4359, memory: 0.0866, power: 0.1385, lr: 0.000050, took: 21.969s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2160/3125, loss: 6.1310, reward: 10.9958, critic_reward: 12.4936, revenue_rate: 0.2786, distance: 4.2962, memory: 0.0293, power: 0.1330, lr: 0.000050, took: 21.948s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2170/3125, loss: 5.2051, reward: 11.2298, critic_reward: 9.8681, revenue_rate: 0.2951, distance: 4.8160, memory: 0.0603, power: 0.1452, lr: 0.000050, took: 21.903s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2180/3125, loss: 2.3352, reward: 11.4585, critic_reward: 11.2184, revenue_rate: 0.2979, distance: 4.7383, memory: 0.0750, power: 0.1510, lr: 0.000050, took: 21.151s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2190/3125, loss: 1.9109, reward: 10.7649, critic_reward: 10.9832, revenue_rate: 0.2814, distance: 4.6936, memory: 0.0536, power: 0.1418, lr: 0.000050, took: 21.282s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2200/3125, loss: 6.2304, reward: 12.4324, critic_reward: 10.5687, revenue_rate: 0.3144, distance: 5.0561, memory: 0.0111, power: 0.1494, lr: 0.000050, took: 22.262s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2210/3125, loss: 3.7369, reward: 10.4540, critic_reward: 11.3341, revenue_rate: 0.2746, distance: 4.5487, memory: 0.0710, power: 0.1348, lr: 0.000050, took: 22.437s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2220/3125, loss: 2.6379, reward: 12.2987, critic_reward: 12.0814, revenue_rate: 0.3172, distance: 5.0629, memory: 0.0405, power: 0.1553, lr: 0.000050, took: 23.286s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2230/3125, loss: 2.9057, reward: 10.8414, critic_reward: 9.8987, revenue_rate: 0.2846, distance: 4.5888, memory: 0.0712, power: 0.1447, lr: 0.000050, took: 24.824s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2240/3125, loss: 2.6199, reward: 10.0296, critic_reward: 9.8234, revenue_rate: 0.2622, distance: 4.1308, memory: 0.0555, power: 0.1294, lr: 0.000050, took: 22.775s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2250/3125, loss: 8.3258, reward: 8.9853, critic_reward: 11.6016, revenue_rate: 0.2356, distance: 3.8715, memory: 0.0521, power: 0.1246, lr: 0.000050, took: 23.527s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2260/3125, loss: 3.8006, reward: 11.6381, critic_reward: 10.5382, revenue_rate: 0.2976, distance: 4.5173, memory: 0.0676, power: 0.1451, lr: 0.000050, took: 24.349s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2270/3125, loss: 3.8707, reward: 11.0159, critic_reward: 9.7123, revenue_rate: 0.2860, distance: 4.7527, memory: 0.0495, power: 0.1410, lr: 0.000050, took: 23.044s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2280/3125, loss: 3.1697, reward: 10.6547, critic_reward: 11.6462, revenue_rate: 0.2758, distance: 4.2883, memory: -0.0013, power: 0.1286, lr: 0.000050, took: 24.055s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2290/3125, loss: 7.4592, reward: 9.7575, critic_reward: 12.0768, revenue_rate: 0.2564, distance: 4.2292, memory: 0.0652, power: 0.1344, lr: 0.000050, took: 23.971s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2300/3125, loss: 9.9694, reward: 11.8908, critic_reward: 9.1130, revenue_rate: 0.3088, distance: 4.8650, memory: 0.0368, power: 0.1478, lr: 0.000050, took: 23.923s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2310/3125, loss: 2.5199, reward: 10.3577, critic_reward: 10.9499, revenue_rate: 0.2709, distance: 4.4604, memory: 0.0219, power: 0.1336, lr: 0.000050, took: 23.745s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2320/3125, loss: 3.3904, reward: 10.5502, critic_reward: 11.0492, revenue_rate: 0.2678, distance: 4.2532, memory: 0.0279, power: 0.1309, lr: 0.000050, took: 26.921s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2330/3125, loss: 1.5851, reward: 10.5741, critic_reward: 10.4040, revenue_rate: 0.2731, distance: 4.2120, memory: 0.0585, power: 0.1313, lr: 0.000050, took: 24.079s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2340/3125, loss: 4.7378, reward: 10.3206, critic_reward: 11.8731, revenue_rate: 0.2646, distance: 4.0851, memory: 0.0275, power: 0.1337, lr: 0.000050, took: 25.518s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2350/3125, loss: 1.6347, reward: 9.7994, critic_reward: 9.9985, revenue_rate: 0.2628, distance: 4.5863, memory: 0.0543, power: 0.1356, lr: 0.000050, took: 22.491s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2360/3125, loss: 2.1945, reward: 11.6789, critic_reward: 11.3496, revenue_rate: 0.2988, distance: 4.5516, memory: -0.0235, power: 0.1390, lr: 0.000050, took: 23.863s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2370/3125, loss: 4.9597, reward: 9.6292, critic_reward: 11.5096, revenue_rate: 0.2504, distance: 4.1023, memory: 0.0782, power: 0.1237, lr: 0.000050, took: 25.945s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2380/3125, loss: 4.8576, reward: 10.0516, critic_reward: 11.2426, revenue_rate: 0.2590, distance: 4.2121, memory: 0.0719, power: 0.1316, lr: 0.000050, took: 24.315s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2390/3125, loss: 3.4370, reward: 10.2066, critic_reward: 11.3231, revenue_rate: 0.2679, distance: 4.5497, memory: 0.0715, power: 0.1406, lr: 0.000050, took: 24.713s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2400/3125, loss: 5.6887, reward: 11.9998, critic_reward: 10.2692, revenue_rate: 0.3092, distance: 4.9565, memory: 0.0124, power: 0.1502, lr: 0.000050, took: 24.885s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2410/3125, loss: 5.2014, reward: 12.1763, critic_reward: 10.7102, revenue_rate: 0.3188, distance: 5.1627, memory: 0.0511, power: 0.1525, lr: 0.000050, took: 24.419s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2420/3125, loss: 1.3863, reward: 10.3980, critic_reward: 10.4432, revenue_rate: 0.2663, distance: 4.3316, memory: 0.0680, power: 0.1357, lr: 0.000050, took: 23.337s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2430/3125, loss: 2.1917, reward: 11.1634, critic_reward: 10.9746, revenue_rate: 0.2883, distance: 4.5431, memory: 0.0587, power: 0.1400, lr: 0.000050, took: 23.794s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2440/3125, loss: 1.2832, reward: 10.0175, critic_reward: 9.8431, revenue_rate: 0.2660, distance: 4.5759, memory: 0.0574, power: 0.1379, lr: 0.000050, took: 24.263s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2450/3125, loss: 8.3681, reward: 10.6104, critic_reward: 12.8945, revenue_rate: 0.2759, distance: 4.8065, memory: 0.0520, power: 0.1395, lr: 0.000050, took: 24.291s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2460/3125, loss: 1.9645, reward: 10.3240, critic_reward: 9.7243, revenue_rate: 0.2730, distance: 4.8638, memory: 0.0798, power: 0.1398, lr: 0.000050, took: 24.566s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2470/3125, loss: 6.7809, reward: 10.0753, critic_reward: 12.0035, revenue_rate: 0.2595, distance: 4.1375, memory: 0.0418, power: 0.1264, lr: 0.000050, took: 23.237s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2480/3125, loss: 5.5777, reward: 9.6133, critic_reward: 11.3638, revenue_rate: 0.2471, distance: 4.0547, memory: 0.0774, power: 0.1226, lr: 0.000050, took: 25.478s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2490/3125, loss: 7.1690, reward: 9.6397, critic_reward: 11.9341, revenue_rate: 0.2552, distance: 4.3682, memory: 0.0821, power: 0.1265, lr: 0.000050, took: 24.887s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2500/3125, loss: 3.4212, reward: 10.7857, critic_reward: 10.1692, revenue_rate: 0.2786, distance: 4.5161, memory: -0.0032, power: 0.1345, lr: 0.000050, took: 24.882s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2510/3125, loss: 4.2844, reward: 10.7489, critic_reward: 12.1831, revenue_rate: 0.2898, distance: 4.9887, memory: 0.0888, power: 0.1434, lr: 0.000050, took: 23.745s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2520/3125, loss: 3.2850, reward: 10.5707, critic_reward: 11.8915, revenue_rate: 0.2725, distance: 4.3132, memory: 0.0324, power: 0.1381, lr: 0.000050, took: 25.238s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2530/3125, loss: 2.8163, reward: 11.2141, critic_reward: 10.0808, revenue_rate: 0.2841, distance: 4.5063, memory: 0.0614, power: 0.1416, lr: 0.000050, took: 24.342s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2540/3125, loss: 4.9729, reward: 12.6481, critic_reward: 11.4109, revenue_rate: 0.3210, distance: 4.8368, memory: -0.0021, power: 0.1504, lr: 0.000050, took: 24.804s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2550/3125, loss: 12.3098, reward: 12.6376, critic_reward: 9.8425, revenue_rate: 0.3236, distance: 5.1799, memory: 0.0378, power: 0.1571, lr: 0.000050, took: 25.251s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2560/3125, loss: 4.9913, reward: 10.0123, critic_reward: 11.7669, revenue_rate: 0.2612, distance: 4.2665, memory: 0.0538, power: 0.1301, lr: 0.000050, took: 24.690s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2570/3125, loss: 2.8857, reward: 11.1586, critic_reward: 10.4566, revenue_rate: 0.2902, distance: 4.5485, memory: 0.0400, power: 0.1380, lr: 0.000050, took: 23.664s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2580/3125, loss: 2.2788, reward: 9.8879, critic_reward: 10.2906, revenue_rate: 0.2565, distance: 4.2650, memory: 0.0323, power: 0.1269, lr: 0.000050, took: 24.054s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2590/3125, loss: 3.1019, reward: 10.7471, critic_reward: 11.3809, revenue_rate: 0.2829, distance: 4.5391, memory: 0.0227, power: 0.1316, lr: 0.000050, took: 24.344s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2600/3125, loss: 9.4446, reward: 13.4334, critic_reward: 11.6437, revenue_rate: 0.3402, distance: 5.3418, memory: 0.0017, power: 0.1585, lr: 0.000050, took: 24.629s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2610/3125, loss: 4.8153, reward: 11.4601, critic_reward: 10.0250, revenue_rate: 0.2943, distance: 4.7157, memory: 0.0005, power: 0.1387, lr: 0.000050, took: 24.251s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2620/3125, loss: 2.1400, reward: 11.0771, critic_reward: 10.4200, revenue_rate: 0.2853, distance: 4.6217, memory: 0.0526, power: 0.1402, lr: 0.000050, took: 23.343s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2630/3125, loss: 2.2601, reward: 11.5334, critic_reward: 10.7923, revenue_rate: 0.2979, distance: 4.6725, memory: 0.0277, power: 0.1455, lr: 0.000050, took: 24.341s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2640/3125, loss: 2.5170, reward: 10.1490, critic_reward: 10.1422, revenue_rate: 0.2606, distance: 4.1762, memory: 0.0366, power: 0.1330, lr: 0.000050, took: 23.152s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2650/3125, loss: 2.6560, reward: 10.8936, critic_reward: 10.4417, revenue_rate: 0.2840, distance: 4.5637, memory: 0.0388, power: 0.1415, lr: 0.000050, took: 23.989s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2660/3125, loss: 8.2011, reward: 11.2706, critic_reward: 9.2488, revenue_rate: 0.2898, distance: 4.6623, memory: 0.0582, power: 0.1446, lr: 0.000050, took: 23.524s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2670/3125, loss: 7.3649, reward: 10.1631, critic_reward: 12.2878, revenue_rate: 0.2604, distance: 4.1089, memory: 0.0569, power: 0.1266, lr: 0.000050, took: 24.121s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2680/3125, loss: 4.3012, reward: 11.3116, critic_reward: 9.9656, revenue_rate: 0.2932, distance: 4.8015, memory: 0.0600, power: 0.1466, lr: 0.000050, took: 23.767s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2690/3125, loss: 2.7810, reward: 9.5063, critic_reward: 9.9762, revenue_rate: 0.2501, distance: 4.0815, memory: 0.0667, power: 0.1266, lr: 0.000050, took: 24.768s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2700/3125, loss: 10.6817, reward: 10.4059, critic_reward: 13.3279, revenue_rate: 0.2737, distance: 4.4890, memory: 0.0422, power: 0.1320, lr: 0.000050, took: 24.863s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2710/3125, loss: 4.6696, reward: 10.5832, critic_reward: 9.0920, revenue_rate: 0.2772, distance: 4.4091, memory: 0.0227, power: 0.1394, lr: 0.000050, took: 23.745s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2720/3125, loss: 1.8596, reward: 11.2999, critic_reward: 11.3626, revenue_rate: 0.2941, distance: 4.5737, memory: -0.0007, power: 0.1396, lr: 0.000050, took: 24.201s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2730/3125, loss: 1.9288, reward: 11.2592, critic_reward: 10.7650, revenue_rate: 0.2917, distance: 4.6608, memory: 0.0574, power: 0.1384, lr: 0.000050, took: 24.710s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2740/3125, loss: 5.9599, reward: 9.9340, critic_reward: 11.5670, revenue_rate: 0.2575, distance: 4.1705, memory: 0.0559, power: 0.1306, lr: 0.000050, took: 23.614s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2750/3125, loss: 13.2451, reward: 12.4472, critic_reward: 9.6186, revenue_rate: 0.3229, distance: 4.8200, memory: 0.0571, power: 0.1535, lr: 0.000050, took: 24.232s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2760/3125, loss: 2.2372, reward: 11.5592, critic_reward: 11.6292, revenue_rate: 0.2986, distance: 4.9452, memory: 0.0934, power: 0.1493, lr: 0.000050, took: 25.103s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2770/3125, loss: 2.7663, reward: 9.9565, critic_reward: 10.8048, revenue_rate: 0.2583, distance: 4.3882, memory: 0.0751, power: 0.1237, lr: 0.000050, took: 23.436s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2780/3125, loss: 3.5837, reward: 11.9372, critic_reward: 12.2619, revenue_rate: 0.3108, distance: 5.0550, memory: 0.0696, power: 0.1573, lr: 0.000050, took: 23.746s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2790/3125, loss: 2.0838, reward: 9.9203, critic_reward: 9.5915, revenue_rate: 0.2559, distance: 4.0410, memory: 0.0505, power: 0.1240, lr: 0.000050, took: 23.446s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2800/3125, loss: 4.5923, reward: 9.8519, critic_reward: 11.0580, revenue_rate: 0.2607, distance: 4.4966, memory: 0.0580, power: 0.1354, lr: 0.000050, took: 24.512s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2810/3125, loss: 4.0577, reward: 11.7071, critic_reward: 10.4714, revenue_rate: 0.3041, distance: 5.0313, memory: 0.0271, power: 0.1461, lr: 0.000050, took: 24.291s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2820/3125, loss: 2.8547, reward: 10.7448, critic_reward: 10.5715, revenue_rate: 0.2732, distance: 4.3204, memory: 0.0075, power: 0.1324, lr: 0.000050, took: 23.601s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2830/3125, loss: 2.2339, reward: 10.8794, critic_reward: 11.1520, revenue_rate: 0.2773, distance: 4.3147, memory: 0.0614, power: 0.1339, lr: 0.000050, took: 23.732s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2840/3125, loss: 4.9595, reward: 10.9237, critic_reward: 9.4883, revenue_rate: 0.2787, distance: 4.3515, memory: -0.0160, power: 0.1359, lr: 0.000050, took: 23.842s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2850/3125, loss: 1.9457, reward: 10.7947, critic_reward: 11.0479, revenue_rate: 0.2819, distance: 4.5868, memory: 0.0266, power: 0.1346, lr: 0.000050, took: 24.231s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2860/3125, loss: 6.0941, reward: 9.3965, critic_reward: 11.4644, revenue_rate: 0.2474, distance: 4.0742, memory: 0.0513, power: 0.1190, lr: 0.000050, took: 24.433s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2870/3125, loss: 2.1067, reward: 10.1021, critic_reward: 10.8499, revenue_rate: 0.2667, distance: 4.5111, memory: 0.0818, power: 0.1341, lr: 0.000050, took: 23.957s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2880/3125, loss: 3.9292, reward: 10.2565, critic_reward: 11.0914, revenue_rate: 0.2658, distance: 4.3483, memory: 0.0104, power: 0.1283, lr: 0.000050, took: 23.938s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2890/3125, loss: 1.7656, reward: 10.1401, critic_reward: 10.4194, revenue_rate: 0.2651, distance: 4.3676, memory: 0.0164, power: 0.1297, lr: 0.000050, took: 22.753s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2900/3125, loss: 6.8647, reward: 9.8773, critic_reward: 12.0160, revenue_rate: 0.2567, distance: 4.3081, memory: 0.0438, power: 0.1308, lr: 0.000050, took: 24.160s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2910/3125, loss: 4.4623, reward: 10.8998, critic_reward: 9.4852, revenue_rate: 0.2761, distance: 4.4815, memory: 0.0486, power: 0.1327, lr: 0.000050, took: 23.951s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2920/3125, loss: 9.2088, reward: 10.2509, critic_reward: 12.6542, revenue_rate: 0.2641, distance: 4.3032, memory: 0.0302, power: 0.1273, lr: 0.000050, took: 24.252s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2930/3125, loss: 3.8441, reward: 9.9822, critic_reward: 11.3394, revenue_rate: 0.2636, distance: 4.2604, memory: 0.0660, power: 0.1341, lr: 0.000050, took: 23.858s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2940/3125, loss: 2.3598, reward: 9.8314, critic_reward: 9.4272, revenue_rate: 0.2521, distance: 4.0144, memory: 0.0752, power: 0.1289, lr: 0.000050, took: 23.904s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2950/3125, loss: 2.6743, reward: 10.1403, critic_reward: 11.1690, revenue_rate: 0.2623, distance: 4.3158, memory: 0.0562, power: 0.1287, lr: 0.000050, took: 22.877s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2960/3125, loss: 3.1955, reward: 10.6784, critic_reward: 9.7387, revenue_rate: 0.2747, distance: 4.3312, memory: 0.0235, power: 0.1385, lr: 0.000050, took: 24.280s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2970/3125, loss: 2.9707, reward: 11.0261, critic_reward: 12.0669, revenue_rate: 0.2858, distance: 4.7304, memory: 0.0406, power: 0.1456, lr: 0.000050, took: 24.795s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2980/3125, loss: 2.0574, reward: 9.8285, critic_reward: 9.8357, revenue_rate: 0.2577, distance: 4.0502, memory: 0.0320, power: 0.1234, lr: 0.000050, took: 23.799s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 2990/3125, loss: 7.2372, reward: 11.1914, critic_reward: 13.0302, revenue_rate: 0.2888, distance: 4.6920, memory: 0.0452, power: 0.1363, lr: 0.000050, took: 24.347s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3000/3125, loss: 2.2591, reward: 11.1920, critic_reward: 11.4531, revenue_rate: 0.2943, distance: 5.0016, memory: 0.0600, power: 0.1418, lr: 0.000050, took: 25.148s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3010/3125, loss: 3.8089, reward: 10.9766, critic_reward: 9.5305, revenue_rate: 0.2860, distance: 4.9100, memory: 0.0768, power: 0.1412, lr: 0.000050, took: 24.018s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3020/3125, loss: 3.0558, reward: 11.1781, critic_reward: 10.8965, revenue_rate: 0.2958, distance: 5.0064, memory: 0.0709, power: 0.1477, lr: 0.000050, took: 25.474s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3030/3125, loss: 5.0096, reward: 11.3454, critic_reward: 10.4718, revenue_rate: 0.2906, distance: 4.8065, memory: 0.0624, power: 0.1466, lr: 0.000050, took: 24.142s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3040/3125, loss: 2.3874, reward: 10.2406, critic_reward: 11.1024, revenue_rate: 0.2619, distance: 4.3311, memory: 0.0453, power: 0.1299, lr: 0.000050, took: 24.115s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3050/3125, loss: 5.8369, reward: 10.2733, critic_reward: 12.3384, revenue_rate: 0.2696, distance: 4.4968, memory: 0.0500, power: 0.1378, lr: 0.000050, took: 24.219s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3060/3125, loss: 1.9601, reward: 10.0752, critic_reward: 9.7399, revenue_rate: 0.2585, distance: 4.0793, memory: 0.0322, power: 0.1256, lr: 0.000050, took: 25.291s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3070/3125, loss: 2.9282, reward: 10.3401, critic_reward: 10.6316, revenue_rate: 0.2726, distance: 4.3271, memory: 0.0135, power: 0.1292, lr: 0.000050, took: 23.469s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3080/3125, loss: 1.9763, reward: 10.7230, critic_reward: 11.2727, revenue_rate: 0.2812, distance: 4.6373, memory: 0.0709, power: 0.1396, lr: 0.000050, took: 24.774s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3090/3125, loss: 3.1754, reward: 10.8799, critic_reward: 9.7891, revenue_rate: 0.2809, distance: 4.6951, memory: 0.0603, power: 0.1412, lr: 0.000050, took: 24.706s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3100/3125, loss: 2.2692, reward: 11.2560, critic_reward: 11.4314, revenue_rate: 0.2977, distance: 5.0821, memory: 0.0751, power: 0.1496, lr: 0.000050, took: 24.865s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3110/3125, loss: 5.1923, reward: 12.4468, critic_reward: 11.2201, revenue_rate: 0.3193, distance: 5.2977, memory: 0.0696, power: 0.1579, lr: 0.000050, took: 25.291s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 3120/3125, loss: 4.0751, reward: 10.5396, critic_reward: 10.8961, revenue_rate: 0.2695, distance: 4.5277, memory: 0.0658, power: 0.1335, lr: 0.000050, took: 24.041s
[GPN+LSTM+Transformer-COOPERATIVE] 开始验证...
[GPN+LSTM+Transformer-COOPERATIVE] 验证完成 - Epoch 1, reward: 10.9556, revenue_rate: 0.2834, distance: 4.5648, memory: 0.0401, power: 0.1387
Epoch 1 摘要:
  训练损失: 4.968229
  训练奖励: 10.779532
  验证奖励: 10.955582
  收益率: 0.283366
  距离: 4.564805
  内存: 0.040142
  功耗: 0.138729
  用时: 7690.35秒
------------------------------------------------------------

[GPN+LSTM+Transformer-COOPERATIVE] 已保存新模型到 ablation_study_2025_08_25_10_41_35\gpn_lstm_transformer_cooperative_2025_08_25_10_41_35 (验证集奖励: 10.9556)

[GPN+LSTM+Transformer-COOPERATIVE] 开始训练 Epoch 2/3
------------------------------------------------------------
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 10/3125, loss: 1.7736, reward: 10.3254, critic_reward: 10.8046, revenue_rate: 0.2657, distance: 4.3760, memory: 0.0350, power: 0.1257, lr: 0.000050, took: 28.476s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 20/3125, loss: 3.4369, reward: 11.2994, critic_reward: 10.4127, revenue_rate: 0.2897, distance: 4.6143, memory: 0.0418, power: 0.1452, lr: 0.000050, took: 24.760s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 30/3125, loss: 3.1660, reward: 9.6839, critic_reward: 10.6327, revenue_rate: 0.2543, distance: 4.2431, memory: 0.0429, power: 0.1212, lr: 0.000050, took: 23.200s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 40/3125, loss: 2.2525, reward: 9.6821, critic_reward: 10.5942, revenue_rate: 0.2532, distance: 4.4033, memory: 0.0748, power: 0.1327, lr: 0.000050, took: 23.349s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 50/3125, loss: 3.5530, reward: 12.6532, critic_reward: 11.6076, revenue_rate: 0.3291, distance: 5.2614, memory: 0.0806, power: 0.1640, lr: 0.000050, took: 25.629s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 60/3125, loss: 3.0386, reward: 11.3480, critic_reward: 10.5313, revenue_rate: 0.2854, distance: 4.3598, memory: 0.0398, power: 0.1420, lr: 0.000050, took: 24.985s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 70/3125, loss: 1.9238, reward: 11.2075, critic_reward: 11.1865, revenue_rate: 0.2942, distance: 4.5367, memory: 0.0407, power: 0.1390, lr: 0.000050, took: 24.141s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 80/3125, loss: 3.3679, reward: 10.3461, critic_reward: 11.5658, revenue_rate: 0.2664, distance: 4.1474, memory: 0.0300, power: 0.1241, lr: 0.000050, took: 24.059s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 90/3125, loss: 3.9562, reward: 11.1115, critic_reward: 11.0601, revenue_rate: 0.2866, distance: 4.5326, memory: 0.0185, power: 0.1384, lr: 0.000050, took: 23.898s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 100/3125, loss: 3.0023, reward: 10.9134, critic_reward: 10.0984, revenue_rate: 0.2837, distance: 4.4588, memory: -0.0081, power: 0.1350, lr: 0.000050, took: 24.656s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 110/3125, loss: 3.9183, reward: 10.2394, critic_reward: 11.5626, revenue_rate: 0.2697, distance: 4.2554, memory: 0.0292, power: 0.1304, lr: 0.000050, took: 23.990s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 120/3125, loss: 2.7072, reward: 11.3850, critic_reward: 10.7731, revenue_rate: 0.2978, distance: 4.7336, memory: 0.0477, power: 0.1549, lr: 0.000050, took: 24.148s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 130/3125, loss: 1.0351, reward: 9.2076, critic_reward: 9.3623, revenue_rate: 0.2370, distance: 3.8652, memory: 0.0581, power: 0.1239, lr: 0.000050, took: 23.016s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 140/3125, loss: 3.1571, reward: 11.1270, critic_reward: 11.7891, revenue_rate: 0.2886, distance: 4.7354, memory: 0.0042, power: 0.1438, lr: 0.000050, took: 24.426s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 150/3125, loss: 2.3265, reward: 11.1646, critic_reward: 11.4005, revenue_rate: 0.2915, distance: 4.5269, memory: 0.0097, power: 0.1405, lr: 0.000050, took: 25.356s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 160/3125, loss: 2.0201, reward: 11.4379, critic_reward: 10.9547, revenue_rate: 0.2925, distance: 4.8226, memory: 0.0079, power: 0.1436, lr: 0.000050, took: 24.593s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 170/3125, loss: 1.2236, reward: 10.3919, critic_reward: 10.1545, revenue_rate: 0.2729, distance: 4.7468, memory: 0.1018, power: 0.1426, lr: 0.000050, took: 24.231s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 180/3125, loss: 3.4023, reward: 10.7299, critic_reward: 10.9415, revenue_rate: 0.2770, distance: 4.3270, memory: 0.0298, power: 0.1396, lr: 0.000050, took: 24.119s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 190/3125, loss: 4.1099, reward: 9.5572, critic_reward: 10.9708, revenue_rate: 0.2512, distance: 4.1723, memory: 0.0391, power: 0.1304, lr: 0.000050, took: 23.337s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 200/3125, loss: 2.8755, reward: 10.6239, critic_reward: 9.6616, revenue_rate: 0.2774, distance: 4.5797, memory: 0.0417, power: 0.1371, lr: 0.000050, took: 23.540s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 210/3125, loss: 2.6200, reward: 10.2048, critic_reward: 11.0205, revenue_rate: 0.2653, distance: 4.4094, memory: 0.0749, power: 0.1373, lr: 0.000050, took: 23.565s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 220/3125, loss: 5.5703, reward: 11.9790, critic_reward: 10.5979, revenue_rate: 0.3063, distance: 4.6110, memory: -0.0038, power: 0.1410, lr: 0.000050, took: 23.425s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 230/3125, loss: 2.1553, reward: 10.9089, critic_reward: 11.0461, revenue_rate: 0.2817, distance: 4.7458, memory: 0.0605, power: 0.1384, lr: 0.000050, took: 24.138s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 240/3125, loss: 2.7536, reward: 10.6656, critic_reward: 10.9737, revenue_rate: 0.2818, distance: 4.5994, memory: 0.0576, power: 0.1368, lr: 0.000050, took: 25.155s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 250/3125, loss: 2.5721, reward: 11.0752, critic_reward: 11.3620, revenue_rate: 0.2817, distance: 4.5791, memory: 0.0143, power: 0.1374, lr: 0.000050, took: 24.454s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 260/3125, loss: 5.3262, reward: 11.0092, critic_reward: 9.1199, revenue_rate: 0.2849, distance: 4.4100, memory: 0.0332, power: 0.1385, lr: 0.000050, took: 23.771s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 270/3125, loss: 4.6233, reward: 10.2765, critic_reward: 11.8453, revenue_rate: 0.2765, distance: 4.4198, memory: 0.0792, power: 0.1355, lr: 0.000050, took: 23.905s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 280/3125, loss: 3.9012, reward: 11.9569, critic_reward: 10.7641, revenue_rate: 0.3062, distance: 4.6700, memory: 0.0380, power: 0.1533, lr: 0.000050, took: 24.593s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 290/3125, loss: 2.3258, reward: 10.0673, critic_reward: 10.2402, revenue_rate: 0.2653, distance: 4.5301, memory: 0.0308, power: 0.1357, lr: 0.000050, took: 24.675s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 300/3125, loss: 3.0586, reward: 11.0229, critic_reward: 11.9383, revenue_rate: 0.2876, distance: 4.7401, memory: 0.0920, power: 0.1410, lr: 0.000050, took: 25.099s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 310/3125, loss: 2.4477, reward: 10.1267, critic_reward: 10.2821, revenue_rate: 0.2632, distance: 4.2303, memory: 0.0683, power: 0.1293, lr: 0.000050, took: 25.575s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 320/3125, loss: 3.0954, reward: 12.0766, critic_reward: 11.1243, revenue_rate: 0.3068, distance: 4.7668, memory: 0.0246, power: 0.1554, lr: 0.000050, took: 24.829s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 330/3125, loss: 2.5439, reward: 10.8360, critic_reward: 10.2775, revenue_rate: 0.2793, distance: 4.4943, memory: 0.0297, power: 0.1352, lr: 0.000050, took: 24.606s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 340/3125, loss: 5.8434, reward: 10.0705, critic_reward: 11.9232, revenue_rate: 0.2650, distance: 4.3664, memory: 0.0182, power: 0.1306, lr: 0.000050, took: 26.094s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 350/3125, loss: 2.9717, reward: 9.8059, critic_reward: 10.4457, revenue_rate: 0.2520, distance: 3.9638, memory: -0.0151, power: 0.1145, lr: 0.000050, took: 23.766s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 360/3125, loss: 13.7731, reward: 13.5596, critic_reward: 10.1609, revenue_rate: 0.3490, distance: 5.7188, memory: 0.1025, power: 0.1720, lr: 0.000050, took: 24.810s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 370/3125, loss: 2.9212, reward: 11.4109, critic_reward: 11.8574, revenue_rate: 0.2987, distance: 4.7823, memory: 0.0447, power: 0.1449, lr: 0.000050, took: 24.934s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 380/3125, loss: 3.4151, reward: 9.9123, critic_reward: 11.1917, revenue_rate: 0.2597, distance: 4.3271, memory: 0.0651, power: 0.1270, lr: 0.000050, took: 24.199s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 390/3125, loss: 4.8609, reward: 11.5087, critic_reward: 9.9454, revenue_rate: 0.2955, distance: 4.8179, memory: 0.0793, power: 0.1478, lr: 0.000050, took: 24.103s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 400/3125, loss: 4.7046, reward: 10.0541, critic_reward: 11.5076, revenue_rate: 0.2566, distance: 4.0867, memory: 0.0321, power: 0.1229, lr: 0.000050, took: 23.942s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 410/3125, loss: 2.0352, reward: 10.2323, critic_reward: 10.3566, revenue_rate: 0.2621, distance: 4.1851, memory: 0.0377, power: 0.1251, lr: 0.000050, took: 24.143s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 420/3125, loss: 5.6644, reward: 10.2951, critic_reward: 12.1794, revenue_rate: 0.2728, distance: 4.5581, memory: 0.0490, power: 0.1339, lr: 0.000050, took: 25.447s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 430/3125, loss: 2.9383, reward: 10.4205, critic_reward: 10.7821, revenue_rate: 0.2660, distance: 4.2149, memory: 0.0080, power: 0.1300, lr: 0.000050, took: 24.339s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 440/3125, loss: 3.2293, reward: 11.3946, critic_reward: 11.9736, revenue_rate: 0.2928, distance: 4.5189, memory: 0.0273, power: 0.1396, lr: 0.000050, took: 24.632s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 450/3125, loss: 2.8646, reward: 10.1745, critic_reward: 10.9383, revenue_rate: 0.2671, distance: 4.4613, memory: 0.0984, power: 0.1350, lr: 0.000050, took: 24.586s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 460/3125, loss: 1.2361, reward: 10.3736, critic_reward: 10.4357, revenue_rate: 0.2706, distance: 4.3917, memory: 0.0294, power: 0.1380, lr: 0.000050, took: 23.474s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 470/3125, loss: 2.1545, reward: 10.6860, critic_reward: 11.5272, revenue_rate: 0.2710, distance: 4.2928, memory: 0.0630, power: 0.1346, lr: 0.000050, took: 24.628s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 480/3125, loss: 2.8041, reward: 10.5329, critic_reward: 9.6515, revenue_rate: 0.2793, distance: 4.5571, memory: 0.0663, power: 0.1358, lr: 0.000050, took: 23.671s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 490/3125, loss: 5.5698, reward: 11.0335, critic_reward: 9.8343, revenue_rate: 0.2792, distance: 4.1981, memory: 0.0156, power: 0.1318, lr: 0.000050, took: 23.523s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 500/3125, loss: 2.2872, reward: 10.6282, critic_reward: 11.5305, revenue_rate: 0.2765, distance: 4.5937, memory: 0.0769, power: 0.1355, lr: 0.000050, took: 24.203s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 510/3125, loss: 2.8176, reward: 11.7190, critic_reward: 11.5958, revenue_rate: 0.2993, distance: 4.8590, memory: 0.0625, power: 0.1530, lr: 0.000050, took: 25.217s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 520/3125, loss: 4.6537, reward: 11.8198, critic_reward: 10.4505, revenue_rate: 0.3034, distance: 4.9175, memory: 0.0209, power: 0.1409, lr: 0.000050, took: 24.196s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 530/3125, loss: 5.0576, reward: 12.6346, critic_reward: 11.5393, revenue_rate: 0.3174, distance: 4.8548, memory: -0.0226, power: 0.1424, lr: 0.000050, took: 24.117s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 540/3125, loss: 4.2155, reward: 11.7713, critic_reward: 10.5801, revenue_rate: 0.3012, distance: 4.9010, memory: 0.0359, power: 0.1518, lr: 0.000050, took: 24.139s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 550/3125, loss: 1.9575, reward: 10.5249, critic_reward: 10.2667, revenue_rate: 0.2700, distance: 4.3367, memory: 0.0383, power: 0.1315, lr: 0.000050, took: 24.254s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 560/3125, loss: 2.4758, reward: 11.4361, critic_reward: 10.7088, revenue_rate: 0.2962, distance: 4.8636, memory: 0.0691, power: 0.1513, lr: 0.000050, took: 24.110s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 570/3125, loss: 2.4536, reward: 10.8854, critic_reward: 10.8259, revenue_rate: 0.2824, distance: 4.6485, memory: 0.0532, power: 0.1399, lr: 0.000050, took: 24.122s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 580/3125, loss: 5.9753, reward: 12.6964, critic_reward: 11.2561, revenue_rate: 0.3263, distance: 5.1954, memory: 0.0635, power: 0.1609, lr: 0.000050, took: 25.543s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 590/3125, loss: 2.4108, reward: 11.0804, critic_reward: 10.7765, revenue_rate: 0.2847, distance: 4.5510, memory: 0.0144, power: 0.1364, lr: 0.000050, took: 24.960s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 600/3125, loss: 3.7331, reward: 9.5254, critic_reward: 11.1031, revenue_rate: 0.2485, distance: 4.2196, memory: 0.0450, power: 0.1220, lr: 0.000050, took: 23.828s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 610/3125, loss: 9.3924, reward: 9.8138, critic_reward: 12.5646, revenue_rate: 0.2568, distance: 4.2315, memory: 0.0503, power: 0.1282, lr: 0.000050, took: 24.577s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 620/3125, loss: 5.8819, reward: 11.2898, critic_reward: 9.5451, revenue_rate: 0.2876, distance: 4.6524, memory: 0.0290, power: 0.1452, lr: 0.000050, took: 23.509s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 630/3125, loss: 3.2029, reward: 10.6346, critic_reward: 11.0160, revenue_rate: 0.2739, distance: 4.1132, memory: 0.0103, power: 0.1324, lr: 0.000050, took: 21.993s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 640/3125, loss: 5.2297, reward: 9.3529, critic_reward: 11.1745, revenue_rate: 0.2406, distance: 3.9599, memory: 0.0739, power: 0.1229, lr: 0.000050, took: 23.067s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 650/3125, loss: 2.1671, reward: 9.9815, critic_reward: 10.0642, revenue_rate: 0.2577, distance: 4.2607, memory: 0.0349, power: 0.1289, lr: 0.000050, took: 21.084s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 660/3125, loss: 3.8870, reward: 11.7461, critic_reward: 11.9551, revenue_rate: 0.3007, distance: 4.8673, memory: 0.0062, power: 0.1469, lr: 0.000050, took: 21.076s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 670/3125, loss: 3.6521, reward: 11.3598, critic_reward: 10.0690, revenue_rate: 0.2918, distance: 4.6879, memory: 0.0176, power: 0.1436, lr: 0.000050, took: 20.413s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 680/3125, loss: 3.0576, reward: 11.0995, critic_reward: 11.3272, revenue_rate: 0.2847, distance: 4.3107, memory: 0.0306, power: 0.1347, lr: 0.000050, took: 21.823s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 690/3125, loss: 3.7383, reward: 11.5089, critic_reward: 10.3912, revenue_rate: 0.2875, distance: 4.2659, memory: 0.0291, power: 0.1373, lr: 0.000050, took: 21.544s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 700/3125, loss: 2.4708, reward: 9.5120, critic_reward: 10.3100, revenue_rate: 0.2443, distance: 4.0952, memory: 0.0457, power: 0.1226, lr: 0.000050, took: 20.831s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 710/3125, loss: 3.0018, reward: 11.4027, critic_reward: 11.5815, revenue_rate: 0.2998, distance: 4.6562, memory: 0.0270, power: 0.1426, lr: 0.000050, took: 21.232s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 720/3125, loss: 1.8570, reward: 9.8531, critic_reward: 9.7701, revenue_rate: 0.2529, distance: 4.0671, memory: 0.0274, power: 0.1244, lr: 0.000050, took: 24.472s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 730/3125, loss: 7.4219, reward: 11.7433, critic_reward: 9.5600, revenue_rate: 0.2970, distance: 4.5599, memory: 0.0329, power: 0.1455, lr: 0.000050, took: 21.590s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 740/3125, loss: 2.9945, reward: 10.8493, critic_reward: 11.6435, revenue_rate: 0.2768, distance: 4.2976, memory: 0.0303, power: 0.1359, lr: 0.000050, took: 22.219s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 750/3125, loss: 3.3951, reward: 11.4588, critic_reward: 10.3610, revenue_rate: 0.2943, distance: 4.8514, memory: 0.0580, power: 0.1480, lr: 0.000050, took: 23.178s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 760/3125, loss: 1.4742, reward: 10.8217, critic_reward: 10.9156, revenue_rate: 0.2787, distance: 4.5918, memory: -0.0022, power: 0.1356, lr: 0.000050, took: 22.999s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 770/3125, loss: 5.7028, reward: 12.0541, critic_reward: 10.8129, revenue_rate: 0.3053, distance: 4.7239, memory: -0.0011, power: 0.1439, lr: 0.000050, took: 21.842s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 780/3125, loss: 2.2838, reward: 10.5434, critic_reward: 10.5086, revenue_rate: 0.2807, distance: 4.6690, memory: 0.0827, power: 0.1365, lr: 0.000050, took: 22.165s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 790/3125, loss: 3.6271, reward: 9.5311, critic_reward: 10.1250, revenue_rate: 0.2566, distance: 4.5221, memory: 0.0686, power: 0.1346, lr: 0.000050, took: 21.925s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 800/3125, loss: 3.3824, reward: 10.6146, critic_reward: 11.5199, revenue_rate: 0.2750, distance: 4.4806, memory: 0.0363, power: 0.1351, lr: 0.000050, took: 22.690s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 810/3125, loss: 3.7705, reward: 11.7957, critic_reward: 10.7108, revenue_rate: 0.3056, distance: 5.2080, memory: 0.0492, power: 0.1450, lr: 0.000050, took: 22.604s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 820/3125, loss: 3.4376, reward: 9.4837, critic_reward: 10.4461, revenue_rate: 0.2491, distance: 4.0368, memory: 0.0413, power: 0.1295, lr: 0.000050, took: 21.376s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 2/3, Batch 830/3125, loss: 3.9967, reward: 10.1384, critic_reward: 11.3819, revenue_rate: 0.2595, distance: 4.1494, memory: -0.0189, power: 0.1177, lr: 0.000050, took: 21.198s
