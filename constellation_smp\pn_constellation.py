"""
定义星座任务规划的PN模型
基于Pointer Network的星座任务规划模型，专门处理4维dynamic数据
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from gpn import Encoder
from attention.attention import MultiHead_Additive_Attention, Additive_Attention_Glimpse
from indrnn.indrnn import IndRNN, IndRNN_Net, IndRNNv2
from constellation_smp.gpn_constellation import ConstellationEncoder

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class ConstellationPointer(nn.Module):
    """
    星座任务规划的Pointer模块，基于PN4SMP的Pointer但适配星座级别的特征
    """
    def __init__(self, hidden_size, num_layers=1, dropout=0.2,
                 attention='MultiHead_Additive_Attention', n_head=8,
                 rnn='indrnn', num_nodes=50):
        super(ConstellationPointer, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # RNN选择
        if rnn == 'gru':
            self.gru = nn.GRU(hidden_size, hidden_size, num_layers,
                              batch_first=True,
                              dropout=dropout if num_layers > 1 else 0)
        elif rnn == 'lstm':
            self.gru = nn.LSTM(hidden_size, hidden_size, num_layers,
                               batch_first=True,
                               dropout=dropout if num_layers > 1 else 0)
        elif rnn == 'indrnn':
            self.gru = IndRNN_Net(hidden_size, hidden_size,
                                  num_nodes, num_layers, IndRNN)
        elif rnn == 'indrnnv2':
            self.gru = IndRNN_Net(hidden_size, hidden_size,
                                  num_nodes, num_layers, IndRNNv2)

        # 注意力机制
        if attention == 'MultiHead_Additive_Attention':
            self.encoder_attn = MultiHead_Additive_Attention(hidden_size, n_head)
        elif attention == 'Additive_Attention_Glimpse':
            self.encoder_attn = Additive_Attention_Glimpse(hidden_size)

        self.drop_rnn = nn.Dropout(p=dropout)
        self.drop_hh = nn.Dropout(p=dropout)

    def forward(self, static_hidden, dynamic_hidden, decoder_hidden, last_hh):
        """
        static_hidden: (batch_size, hidden_size, seq_len) - 静态特征
        dynamic_hidden: (batch_size, hidden_size, seq_len) - 动态特征
        decoder_hidden: (batch_size, hidden_size, 1) - 解码器隐藏状态
        last_hh: RNN的隐藏状态

        返回:
        probs: (batch_size, seq_len) - 任务选择概率
        last_hh: 更新后的RNN隐藏状态
        """
        # RNN处理
        rnn_out, last_hh = self.gru(decoder_hidden.transpose(2, 1), last_hh)
        rnn_out = self.drop_rnn(rnn_out.squeeze(1))

        # 使用注意力机制计算任务选择概率
        if hasattr(self, 'encoder_attn'):
            if isinstance(self.encoder_attn, Additive_Attention_Glimpse):
                # Additive_Attention_Glimpse: 3个参数 (static_hidden, dynamic_hidden, decoder_hidden)
                probs = self.encoder_attn(static_hidden, dynamic_hidden, rnn_out)
            else:
                # MultiHead_Additive_Attention: 2个参数 (q, context)
                # q: (batch_size, hidden_size) - rnn_out
                # context: (batch_size * seq_len, hidden_size) - 合并的特征

                # 合并static和dynamic特征
                combined_features = static_hidden + dynamic_hidden  # (batch_size, hidden_size, seq_len)
                # 重塑为正确的格式: (batch_size * seq_len, hidden_size)
                batch_size, hidden_size, seq_len = combined_features.shape
                combined_flat = combined_features.transpose(1, 2).contiguous().view(batch_size * seq_len, hidden_size)

                # 调用注意力机制
                probs, _ = self.encoder_attn(rnn_out, combined_flat)  # 返回 (probs, context)
        else:
            # 如果没有注意力机制，使用简单的线性层
            probs = torch.randn(rnn_out.size(0), static_hidden.size(2), device=rnn_out.device)

        return probs, last_hh


class PNConstellationEncoder(nn.Module):
    """
    专为PN设计的星座编码器
    直接处理星座数据，避免复杂的特征转换
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites):
        super(PNConstellationEncoder, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites

        # 静态特征编码器
        self.static_encoder = nn.Sequential(
            nn.Linear(static_size, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Dropout(0.1)
        )

        # 动态特征编码器（处理每颗卫星的状态）
        self.dynamic_encoder = nn.Sequential(
            nn.Linear(dynamic_size, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Dropout(0.1)
        )

        # 星座级特征融合
        self.constellation_fusion = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Dropout(0.1)
        )

    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)

        返回:
        task_features: (batch_size, seq_len, hidden_size) - 任务级特征
        satellite_features: (batch_size, seq_len, num_satellites, hidden_size) - 卫星级特征
        """
        batch_size, seq_len = static.size(0), static.size(2)

        # 编码静态特征 (任务特征)
        static_encoded = self.static_encoder(static.transpose(1, 2))  # (batch, seq_len, hidden)

        # 编码动态特征 (每颗卫星的状态)
        dynamic_reshaped = dynamic.permute(0, 2, 3, 1)  # (batch, seq_len, num_satellites, dynamic_size)
        satellite_features = self.dynamic_encoder(dynamic_reshaped)  # (batch, seq_len, num_satellites, hidden)

        # 为每个任务聚合所有卫星的信息
        satellite_aggregated = torch.mean(satellite_features, dim=2)  # (batch, seq_len, hidden)

        # 融合任务特征和卫星聚合特征
        task_features = self.constellation_fusion(
            torch.cat([static_encoded, satellite_aggregated], dim=-1)
        )  # (batch, seq_len, hidden)

        return task_features, satellite_features


class PNConstellation(nn.Module):
    """
    重构的基于Pointer Network的星座任务规划模型
    使用专用编码器和优化的选择机制
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='indrnn', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 attention='MultiHead_Additive_Attention', n_head=8):
        super(PNConstellation, self).__init__()

        if dynamic_size < 1:
            raise ValueError(':param dynamic_size: must be > 0, even if the '
                             'problem has no dynamic elements')

        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.num_layers = num_layers
        self.dropout = dropout

        # 使用专用的PN星座编码器
        self.constellation_encoder = PNConstellationEncoder(
            static_size, dynamic_size, hidden_size, num_satellites
        )

        # 简化的解码器
        self.decoder_rnn = self._create_rnn(hidden_size, hidden_size, rnn, num_layers, dropout)

        # 任务选择的注意力机制
        if attention == 'MultiHead_Additive_Attention':
            self.task_attention = MultiHead_Additive_Attention(hidden_size, n_head)
        else:
            self.task_attention = Additive_Attention_Glimpse(hidden_size, hidden_size)

        # 卫星选择器（基于任务特征和卫星状态）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, 1)  # 输出每颗卫星的选择概率
        )

        # 初始化参数
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def _create_rnn(self, input_size, hidden_size, rnn_type, num_layers, dropout):
        """创建RNN层"""
        if rnn_type.lower() == 'indrnn':
            # IndRNN_Net的参数顺序: (input_size, hidden_size, num_nodes, n_layer)
            # 这里num_nodes设为hidden_size，n_layer设为num_layers
            return IndRNN_Net(input_size, hidden_size, hidden_size, num_layers)
        elif rnn_type.lower() == 'lstm':
            return nn.LSTM(input_size, hidden_size, num_layers,
                          batch_first=True, dropout=dropout if num_layers > 1 else 0)
        else:
            raise ValueError(f"不支持的RNN类型: {rnn_type}")

    def _apply_rnn(self, rnn, input_tensor, hidden_state=None):
        """应用RNN并处理不同类型的返回值"""
        if isinstance(rnn, IndRNN_Net):
            output, hiddens = rnn(input_tensor, hidden_state)
            # 确保输出维度正确: (batch, seq_len, hidden) -> (batch, 1, hidden)
            if output.dim() == 3:
                return output, output[:, -1:, :]  # 取最后一步作为hidden
            else:
                return output, output
        else:  # LSTM
            output, (h_n, c_n) = rnn(input_tensor, hidden_state)
            return output, h_n[-1:].transpose(0, 1)  # 返回最后一层的hidden state

        print(f"PNConstellation: {num_satellites} satellites, {constellation_mode} mode")
        print(f"RNN: {rnn}, layers: {num_layers}")
        print(f"Attention: {attention}, heads: {n_head}")

    def forward(self, static, dynamic):
        """
        重构的前向传播，使用更高效的计算流程

        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)

        返回:
        tour_indices: (batch_size, tour_len) - 选择的任务索引
        satellite_indices: (batch_size, tour_len) - 执行任务的卫星索引
        tour_logp: (batch_size, tour_len) - 任务选择的对数概率
        satellite_logp: (batch_size, tour_len) - 卫星选择的对数概率
        """
        batch_size = static.size(0)
        seq_len = static.size(2)
        device = static.device

        # 预计算所有特征，避免重复计算
        task_features, satellite_features = self.constellation_encoder(static, dynamic)
        # task_features: (batch, seq_len, hidden)
        # satellite_features: (batch, seq_len, num_satellites, hidden)

        # 初始化解码器状态
        decoder_hidden = torch.zeros(batch_size, 1, self.hidden_size, device=device)

        # 初始化结果
        tour_indices = []
        satellite_indices = []
        tour_logp = []
        satellite_logp = []

        # 初始化掩码
        if self.mask_fn is not None:
            mask_result = self.mask_fn(dynamic, static=static)
            if isinstance(mask_result, tuple):
                mask, satellite_masks = mask_result
            else:
                mask = mask_result
                satellite_masks = None
        else:
            mask = torch.ones(batch_size, seq_len, device=device)
            satellite_masks = torch.ones(batch_size, seq_len, self.num_satellites, device=device)

        # 主要选择循环
        for step in range(seq_len):
            # 1. 任务选择
            # 确保decoder_hidden的维度正确 (batch, 1, hidden)
            if decoder_hidden.dim() == 4:
                decoder_hidden = decoder_hidden.squeeze(2)  # 移除多余的维度
            elif decoder_hidden.dim() == 2:
                decoder_hidden = decoder_hidden.unsqueeze(1)  # 添加序列维度

            # 使用注意力机制选择任务
            # 准备注意力机制的输入格式
            query = decoder_hidden.squeeze(1)  # (batch, hidden)
            context = task_features.view(batch_size * seq_len, -1)  # (batch * seq_len, hidden)

            if hasattr(self.task_attention, 'forward'):
                # MultiHead_Additive_Attention 返回 (probs, context)
                task_logits, _ = self.task_attention(query, context)  # (batch, seq_len)
            else:
                # Additive_Attention_Glimpse 只返回 probs
                task_logits = self.task_attention(query, context)

            # 应用任务掩码
            if mask is not None:
                task_logits = task_logits.masked_fill(mask == 0, -1e9)

            task_probs = F.softmax(task_logits, dim=1)

            if self.training:
                task_dist = torch.distributions.Categorical(task_probs)
                task_idx = task_dist.sample()
                task_logp = task_dist.log_prob(task_idx)
            else:
                _, task_idx = torch.max(task_probs, 1)
                task_logp = torch.log(torch.gather(task_probs, 1, task_idx.unsqueeze(1))).squeeze(1)

            # 2. 卫星选择
            # 获取选定任务的特征
            selected_task_features = torch.gather(
                task_features, 1,
                task_idx.unsqueeze(1).unsqueeze(2).expand(-1, 1, self.hidden_size)
            ).squeeze(1)  # (batch, hidden)

            # 获取该任务对应的所有卫星特征
            task_satellite_features = torch.gather(
                satellite_features, 1,
                task_idx.unsqueeze(1).unsqueeze(2).unsqueeze(3).expand(-1, 1, self.num_satellites, self.hidden_size)
            ).squeeze(1)  # (batch, num_satellites, hidden)

            # 为每颗卫星计算选择概率
            satellite_logits = []
            for sat_idx in range(self.num_satellites):
                sat_features = task_satellite_features[:, sat_idx, :]  # (batch, hidden)
                combined_features = torch.cat([selected_task_features, sat_features], dim=1)
                sat_logit = self.satellite_selector(combined_features).squeeze(1)  # (batch,)
                satellite_logits.append(sat_logit)

            satellite_logits = torch.stack(satellite_logits, dim=1)  # (batch, num_satellites)

            # 应用卫星掩码
            if satellite_masks is not None:
                sat_mask = satellite_masks[torch.arange(batch_size), task_idx, :]  # (batch, num_satellites)
                satellite_logits = satellite_logits.masked_fill(sat_mask == 0, -1e9)

            satellite_probs = F.softmax(satellite_logits, dim=1)

            if self.training:
                sat_dist = torch.distributions.Categorical(satellite_probs)
                sat_idx = sat_dist.sample()
                sat_logp = sat_dist.log_prob(sat_idx)
            else:
                _, sat_idx = torch.max(satellite_probs, 1)
                sat_logp = torch.log(torch.gather(satellite_probs, 1, sat_idx.unsqueeze(1))).squeeze(1)

            # 记录选择结果
            tour_indices.append(task_idx)
            satellite_indices.append(sat_idx)
            tour_logp.append(task_logp)
            satellite_logp.append(sat_logp)

            # 3. 更新状态
            # 更新解码器隐藏状态
            decoder_input = selected_task_features.unsqueeze(1)  # (batch, 1, hidden)
            decoder_output, decoder_hidden = self._apply_rnn(self.decoder_rnn, decoder_input, decoder_hidden)

            # 更新动态状态
            if self.update_fn is not None:
                dynamic = self.update_fn(static, dynamic, task_idx, sat_idx)

            # 更新掩码
            if self.mask_fn is not None:
                mask_result = self.mask_fn(dynamic, static=static)
                if isinstance(mask_result, tuple):
                    mask, satellite_masks = mask_result
                else:
                    mask = mask_result

            # 检查是否所有任务都被掩码
            if mask is not None and mask.sum() == 0:
                break

        # 转换为张量
        tour_indices = torch.stack(tour_indices, dim=1)  # (batch, tour_len)
        satellite_indices = torch.stack(satellite_indices, dim=1)  # (batch, tour_len)
        tour_logp = torch.stack(tour_logp, dim=1)  # (batch, tour_len)
        satellite_logp = torch.stack(satellite_logp, dim=1)  # (batch, tour_len)

        return tour_indices, satellite_indices, tour_logp, satellite_logp




class ConstellationStateCriticPN(nn.Module):
    """
    重构的基于PN的星座状态评论家
    使用专用编码器评估星座级别的问题复杂度
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 constellation_mode='cooperative'):
        super(ConstellationStateCriticPN, self).__init__()
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode

        # 使用与策略网络相同的专用编码器
        self.constellation_encoder = PNConstellationEncoder(
            static_size, dynamic_size, hidden_size, num_satellites
        )

        # 评论家网络
        self.fc1 = nn.Conv1d(hidden_size, 20, kernel_size=1)
        self.fc2 = nn.Conv1d(20, 20, kernel_size=1)
        self.fc3 = nn.Conv1d(20, 1, kernel_size=1)

        # 初始化参数
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static, dynamic):
        # 获取星座级别的特征
        task_features, _ = self.constellation_encoder(static, dynamic)
        # task_features: (batch, seq_len, hidden) -> (batch, hidden, seq_len) for Conv1d
        constellation_features = task_features.transpose(1, 2)

        # 评估问题复杂度
        output = F.relu(self.fc1(constellation_features))
        output = F.relu(self.fc2(output))
        output = self.fc3(output).sum(dim=2)

        return output
