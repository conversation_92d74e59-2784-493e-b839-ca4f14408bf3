训练日志 - GPN+LSTM+Transformer - COOPERATIVE 模式
================================================================================
开始时间: 2025-08-25 09:29:06
模型: GPN+LSTM+Transformer
星座模式: cooperative
================================================================================


[GPN+LSTM+Transformer-COOPERATIVE] 开始训练 Epoch 1/3
------------------------------------------------------------
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 10/1563, loss: 296.7810, reward: 11.1887, critic_reward: -5.7658, revenue_rate: 0.2889, distance: 4.6857, memory: 0.0503, power: 0.1429, lr: 0.000100, took: 3.910s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 20/1563, loss: 28.7924, reward: 10.6155, critic_reward: 15.5628, revenue_rate: 0.2751, distance: 4.4715, memory: 0.0293, power: 0.1338, lr: 0.000100, took: 3.583s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 30/1563, loss: 51.2415, reward: 10.9612, critic_reward: 17.7221, revenue_rate: 0.2836, distance: 4.6280, memory: 0.0368, power: 0.1379, lr: 0.000100, took: 3.787s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 40/1563, loss: 12.8601, reward: 11.1203, critic_reward: 7.9230, revenue_rate: 0.2876, distance: 4.5868, memory: 0.0527, power: 0.1418, lr: 0.000100, took: 3.902s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 50/1563, loss: 17.6670, reward: 10.2459, critic_reward: 13.8683, revenue_rate: 0.2693, distance: 4.5919, memory: 0.0377, power: 0.1317, lr: 0.000100, took: 3.614s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 60/1563, loss: 24.7566, reward: 10.4036, critic_reward: 5.7233, revenue_rate: 0.2715, distance: 4.5699, memory: 0.0683, power: 0.1420, lr: 0.000100, took: 3.809s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 70/1563, loss: 10.4552, reward: 10.6513, critic_reward: 13.3118, revenue_rate: 0.2695, distance: 4.3690, memory: 0.0121, power: 0.1288, lr: 0.000100, took: 3.595s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 80/1563, loss: 13.7814, reward: 10.1765, critic_reward: 13.5038, revenue_rate: 0.2634, distance: 4.2449, memory: 0.0441, power: 0.1329, lr: 0.000100, took: 3.654s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 90/1563, loss: 4.2788, reward: 11.5507, critic_reward: 10.5476, revenue_rate: 0.2972, distance: 4.7640, memory: 0.0458, power: 0.1451, lr: 0.000100, took: 3.947s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 100/1563, loss: 4.2350, reward: 11.8220, critic_reward: 11.5018, revenue_rate: 0.3038, distance: 4.8088, memory: 0.0527, power: 0.1508, lr: 0.000100, took: 4.484s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 110/1563, loss: 3.0068, reward: 12.3001, critic_reward: 12.3586, revenue_rate: 0.3215, distance: 5.3377, memory: 0.0717, power: 0.1560, lr: 0.000100, took: 4.531s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 120/1563, loss: 6.1993, reward: 11.9974, critic_reward: 11.3099, revenue_rate: 0.3128, distance: 5.0970, memory: 0.0201, power: 0.1537, lr: 0.000100, took: 4.381s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 130/1563, loss: 6.1358, reward: 11.5927, critic_reward: 9.9883, revenue_rate: 0.2995, distance: 4.7819, memory: 0.0305, power: 0.1460, lr: 0.000100, took: 4.144s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 140/1563, loss: 4.1424, reward: 10.4744, critic_reward: 9.8900, revenue_rate: 0.2742, distance: 4.3972, memory: 0.0415, power: 0.1375, lr: 0.000100, took: 3.707s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 150/1563, loss: 2.9050, reward: 11.0199, critic_reward: 11.6683, revenue_rate: 0.2889, distance: 4.8310, memory: 0.0081, power: 0.1398, lr: 0.000100, took: 3.912s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 160/1563, loss: 2.9761, reward: 10.9979, critic_reward: 11.0438, revenue_rate: 0.2837, distance: 4.6191, memory: 0.0369, power: 0.1418, lr: 0.000100, took: 3.733s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 170/1563, loss: 5.9977, reward: 10.7197, critic_reward: 12.5149, revenue_rate: 0.2743, distance: 4.4036, memory: 0.0315, power: 0.1379, lr: 0.000100, took: 3.643s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 180/1563, loss: 4.1816, reward: 10.6473, critic_reward: 11.0514, revenue_rate: 0.2798, distance: 4.8162, memory: 0.0704, power: 0.1376, lr: 0.000100, took: 3.891s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 190/1563, loss: 4.3389, reward: 10.4962, critic_reward: 10.6300, revenue_rate: 0.2703, distance: 4.4350, memory: 0.0415, power: 0.1343, lr: 0.000100, took: 3.753s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 200/1563, loss: 5.1553, reward: 10.0611, critic_reward: 11.6349, revenue_rate: 0.2575, distance: 4.1257, memory: 0.0196, power: 0.1232, lr: 0.000100, took: 3.714s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 210/1563, loss: 4.3830, reward: 12.5563, critic_reward: 11.5032, revenue_rate: 0.3251, distance: 5.3321, memory: 0.0648, power: 0.1592, lr: 0.000100, took: 4.346s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 220/1563, loss: 4.9672, reward: 11.5467, critic_reward: 12.2405, revenue_rate: 0.2972, distance: 4.6808, memory: 0.0242, power: 0.1421, lr: 0.000100, took: 3.899s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 230/1563, loss: 6.7896, reward: 10.3280, critic_reward: 12.3965, revenue_rate: 0.2643, distance: 4.2880, memory: 0.0364, power: 0.1290, lr: 0.000100, took: 3.504s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 240/1563, loss: 6.7679, reward: 11.0819, critic_reward: 13.0507, revenue_rate: 0.2840, distance: 4.5468, memory: 0.0374, power: 0.1396, lr: 0.000100, took: 3.729s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 250/1563, loss: 2.5685, reward: 10.8823, critic_reward: 10.8923, revenue_rate: 0.2805, distance: 4.5174, memory: 0.0669, power: 0.1417, lr: 0.000100, took: 3.891s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 260/1563, loss: 6.8332, reward: 11.1245, critic_reward: 9.4923, revenue_rate: 0.2818, distance: 4.5513, memory: 0.0427, power: 0.1414, lr: 0.000100, took: 3.987s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 270/1563, loss: 3.9074, reward: 12.8239, critic_reward: 11.9720, revenue_rate: 0.3316, distance: 5.2871, memory: 0.0739, power: 0.1632, lr: 0.000100, took: 4.415s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 280/1563, loss: 2.7966, reward: 11.1670, critic_reward: 11.2129, revenue_rate: 0.2888, distance: 4.8633, memory: 0.0534, power: 0.1461, lr: 0.000100, took: 4.154s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 290/1563, loss: 4.3989, reward: 11.4482, critic_reward: 11.9854, revenue_rate: 0.3008, distance: 4.8329, memory: 0.0240, power: 0.1436, lr: 0.000100, took: 3.940s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 300/1563, loss: 4.2553, reward: 11.8134, critic_reward: 11.9917, revenue_rate: 0.3034, distance: 4.8339, memory: 0.0231, power: 0.1443, lr: 0.000100, took: 4.288s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 310/1563, loss: 5.0519, reward: 11.1299, critic_reward: 9.9932, revenue_rate: 0.2923, distance: 4.7946, memory: 0.0205, power: 0.1469, lr: 0.000100, took: 4.252s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 320/1563, loss: 2.8614, reward: 10.4927, critic_reward: 11.0633, revenue_rate: 0.2756, distance: 4.6901, memory: 0.0835, power: 0.1411, lr: 0.000100, took: 3.851s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 330/1563, loss: 3.2598, reward: 10.5474, critic_reward: 10.8920, revenue_rate: 0.2724, distance: 4.3076, memory: 0.0311, power: 0.1322, lr: 0.000100, took: 3.972s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 340/1563, loss: 2.9998, reward: 11.6114, critic_reward: 12.4574, revenue_rate: 0.2947, distance: 4.7215, memory: 0.0392, power: 0.1415, lr: 0.000100, took: 4.276s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 350/1563, loss: 2.4076, reward: 11.6170, critic_reward: 11.1930, revenue_rate: 0.3007, distance: 4.7224, memory: 0.0114, power: 0.1447, lr: 0.000100, took: 4.240s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 360/1563, loss: 3.6594, reward: 10.7441, critic_reward: 11.1613, revenue_rate: 0.2805, distance: 4.6522, memory: 0.0541, power: 0.1399, lr: 0.000100, took: 4.573s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 370/1563, loss: 3.9844, reward: 11.7565, critic_reward: 12.0573, revenue_rate: 0.2989, distance: 4.8288, memory: 0.0469, power: 0.1457, lr: 0.000100, took: 4.222s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 380/1563, loss: 3.7479, reward: 10.8796, critic_reward: 11.6799, revenue_rate: 0.2810, distance: 4.6794, memory: 0.0478, power: 0.1417, lr: 0.000100, took: 4.160s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 390/1563, loss: 2.9365, reward: 10.5128, critic_reward: 11.2126, revenue_rate: 0.2705, distance: 4.2546, memory: 0.0593, power: 0.1355, lr: 0.000100, took: 3.955s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 400/1563, loss: 5.4867, reward: 11.3812, critic_reward: 9.5982, revenue_rate: 0.2911, distance: 4.7328, memory: 0.0608, power: 0.1377, lr: 0.000100, took: 4.295s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 410/1563, loss: 3.9921, reward: 11.2686, critic_reward: 10.8472, revenue_rate: 0.2866, distance: 4.6886, memory: 0.0133, power: 0.1391, lr: 0.000100, took: 4.098s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 420/1563, loss: 2.5690, reward: 10.8641, critic_reward: 11.4095, revenue_rate: 0.2820, distance: 4.7219, memory: 0.0401, power: 0.1411, lr: 0.000100, took: 4.131s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 430/1563, loss: 4.2005, reward: 12.0049, critic_reward: 11.1822, revenue_rate: 0.3085, distance: 5.1724, memory: 0.0092, power: 0.1491, lr: 0.000100, took: 4.392s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 440/1563, loss: 3.2648, reward: 11.9651, critic_reward: 11.4187, revenue_rate: 0.3084, distance: 5.0053, memory: 0.0486, power: 0.1503, lr: 0.000100, took: 4.328s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 450/1563, loss: 2.2374, reward: 10.1308, critic_reward: 10.3183, revenue_rate: 0.2610, distance: 4.1625, memory: 0.0505, power: 0.1286, lr: 0.000100, took: 4.116s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 460/1563, loss: 3.2746, reward: 12.5217, critic_reward: 12.1886, revenue_rate: 0.3254, distance: 5.2430, memory: 0.0507, power: 0.1597, lr: 0.000100, took: 4.917s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 470/1563, loss: 2.5991, reward: 10.9697, critic_reward: 10.8650, revenue_rate: 0.2856, distance: 4.5532, memory: 0.0282, power: 0.1418, lr: 0.000100, took: 4.329s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 480/1563, loss: 6.1308, reward: 11.8244, critic_reward: 10.1072, revenue_rate: 0.3077, distance: 5.0089, memory: 0.0208, power: 0.1498, lr: 0.000100, took: 4.607s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 490/1563, loss: 3.1743, reward: 10.7614, critic_reward: 11.0973, revenue_rate: 0.2770, distance: 4.3807, memory: 0.0271, power: 0.1350, lr: 0.000100, took: 4.347s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 500/1563, loss: 4.7389, reward: 10.4875, critic_reward: 11.9960, revenue_rate: 0.2670, distance: 4.2387, memory: 0.0413, power: 0.1293, lr: 0.000100, took: 3.848s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 510/1563, loss: 4.9044, reward: 11.7653, critic_reward: 10.5675, revenue_rate: 0.2988, distance: 4.6970, memory: 0.0335, power: 0.1451, lr: 0.000100, took: 4.216s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 520/1563, loss: 2.6742, reward: 10.9237, critic_reward: 10.4181, revenue_rate: 0.2835, distance: 4.7255, memory: 0.0441, power: 0.1361, lr: 0.000100, took: 4.238s
[GPN+LSTM+Transformer-COOPERATIVE] Epoch 1/3, Batch 530/1563, loss: 4.2754, reward: 10.8787, critic_reward: 11.8669, revenue_rate: 0.2795, distance: 4.5120, memory: 0.0109, power: 0.1321, lr: 0.000100, took: 3.943s
