#!/usr/bin/env python3
"""
测试增强版日志系统的脚本
"""
import os
import sys
import torch
import datetime
import numpy as np
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from train_multi_constellation_modes import EnhancedTrainingLogger, train_constellation_with_detailed_logging
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_enhanced_logging():
    """测试增强版日志系统"""
    print("🧪 测试增强版日志系统")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = f"test_logging_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建测试数据集（小规模）
    train_data = ConstellationSMPDataset(
        num_nodes=10,  # 减少节点数以加快测试
        num_samples=20,  # 减少样本数
        seed=1234,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=3  # 减少卫星数
    )
    
    valid_data = ConstellationSMPDataset(
        num_nodes=10,
        num_samples=10,  # 更少的验证样本
        seed=1235,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=3
    )
    
    # 创建模型
    actor = GPNConstellation(
        args.static_size,
        args.dynamic_size,
        args.hidden_size,
        3,  # num_satellites
        args.rnn,
        args.num_layers,
        train_data.update_dynamic,
        train_data.update_mask,
        10,  # num_nodes
        args.dropout,
        'cooperative',  # constellation_mode
        False,  # use_transformer
        None  # transformer_config
    ).to(device)
    
    critic = ConstellationStateCritic(
        args.static_size,
        args.dynamic_size,
        args.hidden_size,
        3,  # num_satellites
        'cooperative'  # constellation_mode
    ).to(device)
    
    # 创建增强版日志记录器
    logger = EnhancedTrainingLogger('cooperative', 'test_gpn', test_dir)
    
    print(f"✓ 创建了日志记录器，保存目录: {test_dir}")
    print(f"✓ 日志文件路径:")
    print(f"  - 主日志: {logger.log_file_path}")
    print(f"  - Epoch摘要: {logger.epoch_log_file_path}")
    print(f"  - 批次详情: {logger.batch_log_file_path}")
    
    # 测试训练（只训练1个epoch）
    print("\n🚀 开始测试训练...")
    
    try:
        best_reward, training_stats = train_constellation_with_detailed_logging(
            actor=actor,
            critic=critic,
            logger=logger,
            task=args.task,
            num_nodes=10,
            train_data=train_data,
            valid_data=valid_data,
            reward_fn=reward,
            render_fn=render,
            batch_size=4,  # 小批次大小
            actor_lr=args.actor_lr,
            critic_lr=args.critic_lr,
            max_grad_norm=args.max_grad_norm,
            attention=args.attention,
            epochs=1,  # 只训练1个epoch
            num_satellites=3,
            save_dir=test_dir,
            weight_decay=args.weight_decay,
            verbose=True,
            constellation_mode='cooperative'
        )
        
        print(f"\n✅ 测试训练完成!")
        print(f"最佳奖励: {best_reward:.4f}")
        print(f"训练统计: {len(training_stats['rewards'])} 个批次")
        
        # 检查日志文件是否创建
        log_files = [
            logger.log_file_path,
            logger.epoch_log_file_path,
            logger.batch_log_file_path
        ]
        
        print(f"\n📋 检查日志文件:")
        for log_file in log_files:
            if os.path.exists(log_file):
                size = os.path.getsize(log_file)
                print(f"✓ {os.path.basename(log_file)}: {size} bytes")
                
                # 显示文件前几行
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:5]
                    print(f"  前5行内容:")
                    for i, line in enumerate(lines, 1):
                        print(f"    {i}: {line.strip()}")
            else:
                print(f"❌ {os.path.basename(log_file)}: 文件不存在")
        
        # 检查JSON摘要文件
        summary_file = os.path.join(test_dir, f'training_summary_test_gpn_cooperative.json')
        if os.path.exists(summary_file):
            print(f"\n✓ 训练摘要文件已创建: {summary_file}")
            with open(summary_file, 'r', encoding='utf-8') as f:
                summary = json.load(f)
                print(f"  总epochs: {summary.get('total_epochs', 0)}")
                print(f"  总批次: {summary.get('total_batches', 0)}")
                print(f"  最佳验证奖励: {summary.get('best_valid_reward', 0):.4f}")
        else:
            print(f"❌ 训练摘要文件未找到")
            
    except Exception as e:
        print(f"❌ 测试训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 增强版日志系统测试完成!")
    print(f"测试结果保存在: {test_dir}")
    return True

if __name__ == '__main__':
    success = test_enhanced_logging()
    if success:
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)
